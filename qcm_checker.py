import os
import re
import tkinter as tk
from tkinter import filedialog, messagebox
from docx import Document
from collections import OrderedDict # To keep order if needed, though dicts are fine for lookup

# --- Helper Functions ---

def normalize_text(text):
    """Remove extra whitespace and standardize."""
    if not text:
        return ""
    return ' '.join(text.strip().split())

def extract_question_data_from_qcm_block(block_text):
    """
    Extracts (Year), Q_Number, and Q_Text from a QCM question block.
    The Q_Text should be only the main question line, not sub-propositions.
    """
    year = None
    q_num = None
    q_text_main = None # Text on the Q line itself

    lines = [line.strip() for line in block_text.split('\n') if line.strip()]
    if not lines:
        return None

    # Find Year and Question Number/Text
    # They might not be in a fixed order or always present (e.g. year for subsequent clinical case questions)
    for line in lines:
        year_match = re.match(r"^\((\d{4})\)$", line)
        if year_match and year is None: # Take first year found
            year = year_match.group(1)

        q_match = re.match(r"^Q(\d+)\.\s*(.*)", line, re.IGNORECASE) # Q or q
        if q_match and q_num is None: # Take first question found in block
            q_num = int(q_match.group(1))
            q_text_main = normalize_text(q_match.group(2))
            # We found the primary question line, no need to look further in this block for Q text
            # (as choices, reponse, or numbered propositions would follow)
            break # Found main question info for this block

    if q_num is not None and q_text_main is not None:
        return {
            "year": year, # Can be None if not found for this specific question block
            "number": q_num,
            "text": q_text_main
        }
    return None


def parse_qcm_file(filepath):
    """
    Parses a QCM docx file.
    Returns a list of dictionaries: [{"year": YYYY, "number": NN, "text": "Question Text"}, ...]
    """
    try:
        doc = Document(filepath)
    except Exception as e:
        print(f"Error opening/reading QCM file {filepath}: {e}")
        return []

    full_text = "\n".join([p.text for p in doc.paragraphs])
    questions = []

    # Primary separator for questions or (ClinicalCase + its FirstQuestion)
    # We add a separator at the end to ensure the last block is processed by split
    if not full_text.strip().endswith("####################"):
        full_text += "\n####################"

    raw_blocks = full_text.split("####################")

    for block_content in raw_blocks:
        block_content = block_content.strip()
        if not block_content:
            continue

        # A block might be:
        # 1. Standard QCM/QROC
        # 2. (Year)\nCas clinique :\n{Text}\n********************\n(Year)\nQNN. {Text}... (First question of a clinical case)

        if "Cas clinique :" in block_content and "********************" in block_content:
            # This block contains a clinical case introduction AND its first question.
            # The text for the question itself is *after* "********************"
            parts = block_content.split("********************", 1)
            if len(parts) == 2:
                question_part_of_clinical_case = parts[1].strip()
                if question_part_of_clinical_case: # Ensure there's content after separator
                    q_data = extract_question_data_from_qcm_block(question_part_of_clinical_case)
                    if q_data:
                        questions.append(q_data)
            # else: malformed clinical case block, might be caught by generic parsing or skipped
        else:
            # This is a standard question block, or a subsequent question of a clinical case
            q_data = extract_question_data_from_qcm_block(block_content)
            if q_data:
                questions.append(q_data)

    return questions


def extract_question_data_from_comment_block(block_text):
    """
    Extracts Q_Number and Q_Text from a comment block.
    The Q_Text is expected on the first line.
    """
    lines = [line.strip() for line in block_text.split('\n') if line.strip()]
    if not lines:
        return None

    first_line = lines[0]
    # Regex for '{NN}. text' or 'Q{NN}. text'
    # Match Q (optional), then digits, then dot, then any text.
    match = re.match(r"^(?:Q)?(\d+)\.\s*(.*)", first_line, re.IGNORECASE) # Q or q
    if match:
        q_num = int(match.group(1))
        q_text = normalize_text(match.group(2))
        return {
            "number": q_num,
            "text": q_text
            # Comment body is in lines[1:], ignored for matching
        }
    # print(f"DEBUG: Comment regex failed for line: '{first_line}'")
    return None

def parse_comment_file(filepath):
    """
    Parses a comment docx file.
    Returns a list of dictionaries: [{"number": NN, "text": "Question Text"}, ...]
    """
    try:
        doc = Document(filepath)
    except Exception as e:
        print(f"Error opening/reading comment file {filepath}: {e}")
        return []

    full_text = "\n".join([p.text for p in doc.paragraphs])
    comments = []

    # Add a separator at the end to ensure the last block is processed by split
    if not full_text.strip().endswith("####################"):
        full_text += "\n####################"

    raw_blocks = full_text.split("####################")

    for block_content in raw_blocks:
        block_content = block_content.strip()
        if not block_content:
            continue

        # Clinical case text is skipped in comments, only questions are commented.
        # So, each block should directly be a comment for a question.
        comment_data = extract_question_data_from_comment_block(block_content)
        if comment_data:
            comments.append(comment_data)

    return comments

# --- Folder Selection Functions ---

def select_folder(title="Select Folder"):
    """
    Opens a folder selection dialog and returns the selected folder path.
    Returns None if user cancels.
    """
    root = tk.Tk()
    root.withdraw()  # Hide the main window

    folder_path = filedialog.askdirectory(title=title)
    root.destroy()

    return folder_path if folder_path else None

def get_folder_paths():
    """
    Prompts user to select QCM and Comments folders.
    Returns tuple (qcm_folder, comments_folder) or (None, None) if cancelled.
    """
    print("Please select the folders containing your files...")

    # Select QCM folder
    qcm_folder = select_folder("Select QCM Folder (containing .docx files)")
    if not qcm_folder:
        print("QCM folder selection cancelled.")
        return None, None

    # Select Comments folder
    comments_folder = select_folder("Select Comments Folder (containing .docx files)")
    if not comments_folder:
        print("Comments folder selection cancelled.")
        return None, None

    print(f"QCM Folder: {qcm_folder}")
    print(f"Comments Folder: {comments_folder}")

    return qcm_folder, comments_folder

# --- Main Logic ---

def compare_qcm_and_comments(qcm_folder, comments_folder):
    qcm_files = [f for f in os.listdir(qcm_folder) if f.endswith('.docx') and not f.startswith('~')]

    if not qcm_files:
        print(f"No .docx files found in QCM folder: {qcm_folder}")
        return

    print(f"Found {len(qcm_files)} QCM files to process.\n")

    for qcm_filename in qcm_files:
        qcm_filepath = os.path.join(qcm_folder, qcm_filename)
        comment_filepath = os.path.join(comments_folder, qcm_filename) # Assumes same filename

        print(f"--- Comparing File: {qcm_filename} ---")

        if not os.path.exists(comment_filepath):
            print(f"  ERROR: Comment file not found: {comment_filepath}\n")
            continue

        qcm_questions = parse_qcm_file(qcm_filepath)
        comment_entries = parse_comment_file(comment_filepath)

        if not qcm_questions and not comment_entries:
            print("  INFO: Both QCM and Comment files appear to be empty or unparsable.\n")
            continue
        if not qcm_questions:
            print("  INFO: QCM file appears to be empty or unparsable. Comments found.")
            for c_entry in comment_entries:
                 print(f"  Comment Q{c_entry['number']:02d}: Found in comments, but no QCMs parsed.")
            print("") #newline
            continue
        if not comment_entries:
            print("  INFO: Comment file appears to be empty or unparsable. QCMs found.")
            for q_entry in qcm_questions:
                print(f"  QCM Q{q_entry['number']:02d}: Found in QCMs, but no comments parsed.")
            print("") #newline
            continue


        # Create dictionaries for quick lookup by question number
        qcm_dict = {q['number']: q for q in qcm_questions}
        # Handle potential duplicate question numbers in comments by taking the first one?
        # Or alert? For now, simple dict creation. If duplicates, last one wins.
        comment_dict = {c['number']: c for c in comment_entries}

        all_q_numbers = sorted(list(set(qcm_dict.keys()) | set(comment_dict.keys())))

        match_count = 0
        mismatch_text_count = 0
        missing_in_comments_count = 0
        missing_in_qcm_count = 0

        for q_num in all_q_numbers:
            qcm_entry = qcm_dict.get(q_num)
            comment_entry = comment_dict.get(q_num)

            if qcm_entry and comment_entry:
                # Normalize texts for comparison just in case (though parsers should do it)
                qcm_text_norm = normalize_text(qcm_entry['text'])
                comment_text_norm = normalize_text(comment_entry['text'])

                if qcm_text_norm == comment_text_norm:
                    print(f"  Q{q_num:02d}: MATCH")
                    match_count += 1
                else:
                    print(f"  Q{q_num:02d}: MISMATCH - Question text differs")
                    print(f"    QCM Text:     '{qcm_text_norm}'")
                    print(f"    Comment Text: '{comment_text_norm}'")
                    mismatch_text_count +=1
            elif qcm_entry and not comment_entry:
                print(f"  Q{q_num:02d}: MISSING in Comments file (QCM Text: '{normalize_text(qcm_entry['text'])}')")
                missing_in_comments_count +=1
            elif not qcm_entry and comment_entry:
                print(f"  Q{q_num:02d}: MISSING in QCM file (Comment Text: '{normalize_text(comment_entry['text'])}')")
                missing_in_qcm_count += 1

        print("\n  Summary for this file:")
        print(f"    Total unique question numbers processed: {len(all_q_numbers)}")
        print(f"    Perfect matches (number and text): {match_count}")
        print(f"    Mismatched text (same number): {mismatch_text_count}")
        print(f"    Questions in QCM missing in Comments: {missing_in_comments_count}")
        print(f"    Comments found for non-existent QCM questions: {missing_in_qcm_count}")

        if len(qcm_questions) != len(comment_entries) and (missing_in_comments_count > 0 or missing_in_qcm_count > 0) :
             print(f"    Overall Count: QCM file has {len(qcm_questions)} questions, Comment file has {len(comment_entries)} comment blocks.")
        elif len(qcm_questions) != len(comment_entries): # Counts differ but all numbers matched somehow (e.g. duplicates in one file)
             print(f"    NOTE: QCM file has {len(qcm_questions)} parsed question items, Comment file has {len(comment_entries)} parsed comment items, but all question numbers were accounted for.")
        print("-" * 30 + "\n")


# --- Main Execution ---
if __name__ == "__main__":
    print("QCM and Comments File Checker")
    print("=" * 40)

    # Get folder paths from user
    qcm_folder, comments_folder = get_folder_paths()

    if qcm_folder and comments_folder:
        print("\nStarting comparison...\n")
        compare_qcm_and_comments(qcm_folder, comments_folder)
        print("\nComparison finished.")
    else:
        print("Operation cancelled. No folders selected.")

    # Optional: Keep console open for user to see results
    input("\nPress Enter to exit...")