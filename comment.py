import re
import os
import glob
from docx import Document
from docx.shared import Pt

def parse_questions_file(filepath):
    """
    Parses the QCM DOCX file.
    Returns a list of dictionaries: [{'number': '01', 'text': 'Question text...'}, ...]
    """
    doc = Document(filepath)
    questions = []
    # Regex to find QXX. Question lines
    q_line_regex = re.compile(r"^\s*Q(\d{2})\.\s*(.*)")
    # Regex for separators (hashes or stars)
    separator_regex = re.compile(r"^(?:#|\*){20,}$")
    # Regex for year lines like (2021)
    year_regex = re.compile(r"^\s*\(\d{4}\)\s*$")
    # Regex for response lines
    response_regex = re.compile(r"^\s*Réponse\s*:\s*.*")
    # Regex for option lines
    option_regex = re.compile(r"^\s*[A-E]\.\s+.*")

    for para in doc.paragraphs:
        text = para.text.strip() # Strip leading/trailing whitespace from the line
        if not text: continue

        # Skip lines that are definitely not the start of a question's main text
        if year_regex.match(text) or \
           response_regex.match(text) or \
           option_regex.match(text) or \
           separator_regex.match(text):
            continue

        # Match on the stripped text for QXX.
        q_match = q_line_regex.match(text)
        if q_match:
            q_num = q_match.group(1)
            # Group 2 is the rest of the line after "QXX. "
            q_text_content = q_match.group(2).strip() # Strip again in case of spaces after QXX.
            questions.append({'number': q_num, 'text': q_text_content})
    return questions


def parse_comments_file(filepath):
    """
    Parses the original comments DOCX file using a refined regex.
    Returns a dictionary: {'01': ['para1_text', 'para2_text', ...], ...}
    """
    doc = Document(filepath)
    comments_map = {}
    current_q_num = None
    current_comment_paragraphs = []

    # Refined Regex for comment headers:
    # - Handles "Q" or "QCM" (optional)
    # - Captures 1 or 2 digits for the question number.
    # - Allows for various dash characters, colon, or period as separator, surrounded by optional spaces.
    # - Captures the rest of the line as the question text.
    # - The `re.IGNORECASE` makes "Q" or "q" (and "QCM" or "qcm") match.
    comment_header_regex = re.compile(
        r"^(?:Q|QCM)?\s*(\d{1,2})\s*[-\–—.:]\s*(.*)", re.IGNORECASE
    )
    # Explanation of regex:
    # ^                     : Start of the line
    # (?:Q|QCM)?            : Optionally "Q" or "QCM" (non-capturing group)
    # \s*                   : Zero or more whitespace characters
    # (\d{1,2})             : Capture 1 or 2 digits (this is Group 1 - the question number)
    # \s*                   : Zero or more whitespace characters
    # [-\–—.:]              : Exactly one character from this set (hyphen, en-dash, em-dash, period, colon)
    # \s*                   : Zero or more whitespace characters
    # (.*)                  : Capture any characters to the end of the line (this is Group 2 - the question text from comment)

    # print(f"    DEBUG: Using comment_header_regex: {comment_header_regex.pattern}") # For debugging

    for para_idx, para in enumerate(doc.paragraphs):
        text_to_match = para.text.strip() # Important to strip the line first

        # if "Q01 -" in text_to_match or "Q1 -" in text_to_match: # For targeted debugging
        #    print(f"    DEBUG: Trying to match header in para {para_idx}: '{text_to_match}'")

        header_match = comment_header_regex.match(text_to_match)

        if header_match:
            # print(f"    DEBUG: Matched header: '{text_to_match}' -> Q_num from comment: {header_match.group(1)}")
            if current_q_num and current_comment_paragraphs:
                comments_map[current_q_num] = current_comment_paragraphs
            
            current_q_num = header_match.group(1).zfill(2) # Standardize to "01", "02"
            # The actual question text from the comment header (group 2) is not used beyond identification of the line as a header.
            current_comment_paragraphs = [] # Reset for the new comment body
        elif current_q_num:
            # This line is part of the body of the currently active comment
            current_comment_paragraphs.append(para.text) # Keep original para.text with its formatting
        # else:
            # This line is not a header and no comment is active (e.g. blank lines at start, or text before first valid header)
            # print(f"    DEBUG: Line not a header and no active comment: '{text_to_match}'")
            pass

    # Save the last comment block
    if current_q_num and current_comment_paragraphs:
        comments_map[current_q_num] = current_comment_paragraphs
    
    # if not comments_map:
    #     print(f"    WARNING: No comments were parsed from the file: {os.path.basename(filepath)}")
    # else:
    #     print(f"    DEBUG: Parsed comment Q numbers from file: {list(comments_map.keys())}")

    return comments_map


def create_formatted_comment_docx(qcm_data_list, comments_map_from_file, output_filepath):
    """
    Creates the new, correctly formatted comments DOCX.
    qcm_data_list is the list of {'number': ..., 'text': ...} from parse_questions_file (canonical)
    comments_map_from_file uses Q numbers parsed from the comment file as keys.
    """
    new_doc = Document()
    style = new_doc.styles['Normal']
    font = style.font
    font.name = 'Calibri'
    font.size = Pt(11)

    first_comment_block_written_to_output = True
    
    for q_info in qcm_data_list: # Iterate based on the order and content of QCM file
        canonical_q_num = q_info['number']
        canonical_q_text = q_info['text']

        # Check if the Q number parsed from the comment file exists for this canonical Q number
        if canonical_q_num in comments_map_from_file:
            comment_paragraphs_list = comments_map_from_file[canonical_q_num]

            if not first_comment_block_written_to_output:
                separator_para = new_doc.add_paragraph("####################")
                sep_pf = separator_para.paragraph_format
                sep_pf.line_spacing = 1.5
                sep_pf.space_after = Pt(6)
            
            # Use the canonical question text for the header in the output file
            header_para = new_doc.add_paragraph(f"Q{canonical_q_num}. {canonical_q_text}")
            hp_pf = header_para.paragraph_format
            hp_pf.line_spacing = 1.5
            hp_pf.space_after = Pt(6)

            for comment_para_text in comment_paragraphs_list:
                added_comment_para = new_doc.add_paragraph(comment_para_text)
                cp_pf = added_comment_para.paragraph_format
                cp_pf.line_spacing = 1.5
                cp_pf.space_after = Pt(6)
            
            first_comment_block_written_to_output = False
        # else:
            # This means a question QXX exists in QCM file but no comment for QXX was found in comment file
            # print(f"    Note: No comment found in comment file for canonical Q{canonical_q_num}. Skipping in output.")

    new_doc.save(output_filepath)


# --- Main Execution ---
qcm_folder_path = './QCM_Files'
comments_folder_path = './Comment_Files'
output_folder_path = './Output_Formatted_Comments'

if not os.path.exists(output_folder_path):
    os.makedirs(output_folder_path)
    print(f"Created output directory: {output_folder_path}")

qcm_files_pattern = os.path.join(qcm_folder_path, '*.docx')
qcm_file_paths = glob.glob(qcm_files_pattern)

if not qcm_file_paths:
    print(f"No .docx files found in QCM folder: {qcm_folder_path}")
else:
    print(f"Found {len(qcm_file_paths)} QCM files to process.\n")

for qcm_file_path_iter in qcm_file_paths:
    qcm_filename = os.path.basename(qcm_file_path_iter)
    print(f"Processing QCM file: {qcm_filename}")

    comment_filename = qcm_filename
    corresponding_comment_path = os.path.join(comments_folder_path, comment_filename)

    if not os.path.exists(corresponding_comment_path):
        print(f"  ERROR: Corresponding comment file not found: {corresponding_comment_path}")
        print(f"  Skipping QCM file: {qcm_filename}\n")
        continue

    print(f"  Found corresponding comment file: {comment_filename}")
    formatted_comments_output_path = os.path.join(output_folder_path, qcm_filename)

    try:
        # 1. Parse the QCM file to get canonical questions (list of dicts for order)
        canonical_questions_list = parse_questions_file(qcm_file_path_iter)
        if not canonical_questions_list:
            print(f"    WARNING: No questions found in QCM file: {qcm_filename}. Output might be empty.")
            # continue # Decide if you want to skip or produce empty output
        print(f"    Parsed {len(canonical_questions_list)} questions from QCM file.")

        # 2. Parse the corresponding comments file
        # This returns a map where keys are Q numbers extracted *from the comment file headers*
        comments_data_from_file = parse_comments_file(corresponding_comment_path)
        if not comments_data_from_file:
            print(f"    WARNING: No comments parsed from comment file: {comment_filename}.")
        else:
            print(f"    Parsed comments for Q numbers {list(comments_data_from_file.keys())} from comments file.")
        
        # 3. Create the new formatted comment DOCX
        # It will iterate through canonical_questions_list and use its Q numbers to look up in comments_data_from_file
        create_formatted_comment_docx(canonical_questions_list, comments_data_from_file, formatted_comments_output_path)
        print(f"  Successfully processed and created output for: {qcm_filename}\n")

    except Exception as e:
        print(f"  ERROR processing file {qcm_filename}: {e}\n")
        import traceback
        traceback.print_exc()

print("Batch processing complete.")