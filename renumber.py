import os
import re
from docx import Document
import tkinter as tk
from tkinter import filedialog

def get_folder_path():
    """Opens a dialog for the user to select a folder."""
    root = tk.Tk()
    root.withdraw()  # Hide the main tkinter window
    folder_path = filedialog.askdirectory(title="Select Folder Containing DOCX Files")
    if folder_path:
        print(f"Selected folder: {folder_path}")
    else:
        print("No folder selected.")
    return folder_path

def renumber_questions_in_folder(folder_path, start_number=1):
    """
    Renames questions in DOCX files within a given folder.
    Questions are searched for a pattern like "Q<number>." (e.g., "Q198. ").
    The renumbering is sequential across all processed files, with leading zeros for numbers < 10.
    Any text preceding the "Q<number>." on the same line is preserved.

    Args:
        folder_path (str): The path to the folder containing DOCX files.
        start_number (int): The number to start renumbering from.
    """
    if not folder_path or not os.path.isdir(folder_path):
        if folder_path: # Only print error if a path was given but invalid
            print(f"Error: Folder '{folder_path}' not found or is not a directory.")
        return

    current_question_number = start_number
    # Regex to find "Q", then digits, then a literal dot.
    # This will allow text before the "Q".
    # Group 1: "Q"
    # Group 2: The original number (e.g., "198")
    # Group 3: The dot and the rest of the question text (e.g., ". Le médicament :")
    question_pattern = re.compile(r"(Q)(\d+)(\..*)") # Removed ^ from start and $ from end

    docx_files = sorted([f for f in os.listdir(folder_path) if f.lower().endswith('.docx')])

    if not docx_files:
        print(f"No .docx files found in '{folder_path}'.")
        return

    print(f"\nStarting renumbering from Q{start_number:02d}...\n")

    for filename in docx_files:
        filepath = os.path.join(folder_path, filename)
        print(f"Processing file: {filename}")
        
        try:
            doc = Document(filepath)
            modified_in_file = False
            
            for para in doc.paragraphs:
                # Use re.search() to find the pattern anywhere in the line
                match = question_pattern.search(para.text) 
                if match:
                    old_question_line = para.text
                    
                    # Preserve text before the matched "Q"
                    prefix = para.text[:match.start(1)] # Text before the "Q"
                    
                    # The rest of the question text starts from the dot
                    question_suffix = match.group(3) 
                    
                    new_question_text = f"{prefix}Q{current_question_number:02d}{question_suffix}"
                    para.text = new_question_text
                    
                    print(f"  - Renumbered: '{old_question_line}' -> '{new_question_text}'")
                    
                    current_question_number += 1
                    modified_in_file = True
            
            if modified_in_file:
                doc.save(filepath)
                print(f"  Saved changes to {filename}\n")
            else:
                print(f"  No questions found matching the pattern in {filename}.\n")

        except Exception as e:
            print(f"  Error processing file {filename}: {e}\n")

    print(f"Renumbering complete. Next question number would be Q{current_question_number:02d}.")

# --- How to use ---
if __name__ == "__main__":
    target_folder = get_folder_path()
    
    if target_folder:
        # --- !!! MAKE A BACKUP OF YOUR FOLDER BEFORE RUNNING !!! ---
        confirmation = input(f"This script will modify DOCX files in '{target_folder}'.\n"
                             "Have you backed up this folder? (yes/no): ").lower()
        if confirmation == 'yes':
            renumber_questions_in_folder(target_folder, start_number=1)
        else:
            print("Operation cancelled. Please back up your folder and then run the script again.")
    else:
        print("Operation cancelled as no folder was selected.")