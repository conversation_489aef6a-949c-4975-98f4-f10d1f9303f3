import os
import re
from docx import Document
import shutil # To optionally copy files first

def convert_qcm_options(input_folder, output_folder):
    """
    Processes DOCX files in input_folder, converts lowercase option letters
    (a., b., c.) to uppercase (A., B., C.), and saves the modified files
    to output_folder.

    Args:
        input_folder (str): Path to the folder containing original DOCX files.
        output_folder (str): Path to the folder where converted files will be saved.
    """

    # Regex to find lines starting with a single lowercase letter, dot, and space
    # ^\s* : Start of line, optional leading whitespace
    # ([a-z]) : Capture group 1: a single lowercase letter
    # \. : Match a literal dot
    # \s+ : Match one or more whitespace characters (flexible)
    # (.*) : Capture group 2: the rest of the line content
    option_pattern = re.compile(r"^\s*([a-z])\.\s+(.*)")

    # Create the output folder if it doesn't exist
    os.makedirs(output_folder, exist_ok=True)
    print(f"Output will be saved in: {output_folder}")

    converted_files_count = 0
    total_options_changed = 0

    # Iterate through all files in the input folder
    for filename in os.listdir(input_folder):
        # Process only .docx files
        if filename.lower().endswith(".docx"):
            input_filepath = os.path.join(input_folder, filename)
            output_filepath = os.path.join(output_folder, filename)

            print(f"\nProcessing file: {filename}...")

            try:
                # Open the document
                document = Document(input_filepath)
                needs_saving = False
                file_options_changed = 0

                # Iterate through each paragraph in the document
                for para in document.paragraphs:
                    # Check if the paragraph text matches our option pattern
                    match = option_pattern.match(para.text)
                    if match:
                        original_text = para.text
                        leading_whitespace = original_text[:original_text.find(match.group(1))] # Capture exact leading space
                        letter = match.group(1)
                        rest_of_line = match.group(2)

                        # Convert letter to uppercase
                        uppercase_letter = letter.upper()

                        # Reconstruct the line
                        # Ensure we preserve the original leading whitespace
                        new_text = f"{leading_whitespace}{uppercase_letter}. {rest_of_line}"

                        # Only modify if the text actually changes
                        if para.text != new_text:
                           para.text = new_text
                           file_options_changed += 1
                           needs_saving = True

                # Save the modified document to the output folder ONLY if changes were made
                if needs_saving:
                    document.save(output_filepath)
                    print(f"  Converted {file_options_changed} options. Saved as: {output_filepath}")
                    total_options_changed += file_options_changed
                    converted_files_count += 1
                else:
                    print(f"  No convertible options found or no changes needed. Skipped saving.")
                    # Optional: Copy unchanged files to output folder if you want all files there
                    # shutil.copy2(input_filepath, output_filepath)
                    # print(f"  Copied unchanged file to output folder.")


            except Exception as e:
                print(f"  [ERROR] Could not process file {filename}: {e}")
                # Optionally skip or log the error further

    print("\n--------------------------------------------------")
    print("Processing complete.")
    print(f"Total files processed where changes were made: {converted_files_count}")
    print(f"Total options converted across all files: {total_options_changed}")
    print(f"Converted files are located in: '{output_folder}'")
    print("Original files remain untouched in the input folder.")
    print("IMPORTANT: Review the converted files. While the script aims to preserve content,")
    print("           complex formatting within paragraphs might be affected.")
    print("           Always keep backups of your original files!")


# --- Script Execution ---
if __name__ == "__main__":
    # Get the input folder path from the user
    input_dir = input("Enter the path to the folder containing your QCM DOCX files: ")

    # Validate if the input directory exists
    if not os.path.isdir(input_dir):
        print(f"Error: The folder '{input_dir}' does not exist. Please check the path.")
    else:
        # Define the output folder name (e.g., a subfolder named 'converted')
        output_dir = os.path.join(input_dir, "converted_format")

        # Run the conversion function
        convert_qcm_options(input_dir, output_dir)