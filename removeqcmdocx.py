import re
import os
import tkinter as tk
from tkinter import filedialog
import docx # The python-docx library
from docx.oxml.ns import qn # Namespace helper for XML element manipulation
from docx.oxml import OxmlElement # For XML manipulation

# --- Regex Patterns ---
YEAR_PATTERN = re.compile(r"\b(19|20)\d{2}\b")
QCM_QCS_PATTERN = re.compile(r"^(QCM|QCS)$", re.IGNORECASE)
QUESTION_PATTERN = re.compile(r"^(Q\d+|\d+\.)") # Check if line *starts* with this

def remove_paragraph(paragraph):
    """
    Removes a paragraph element from its parent in the document XML structure.
    """
    p_element = paragraph._element
    p_element.getparent().remove(p_element)
    # paragraph._p = paragraph._element = None # Optional: Clean up internal references

def process_docx_document(doc):
    """
    Processes a python-docx Document object to remove specific QCM/QCS paragraphs
    based on the surrounding context (Year -> QCM/QCS -> Question).

    Args:
        doc (docx.Document): The Document object to process.

    Returns:
        bool: True if any paragraphs were removed, False otherwise.
    """
    paragraphs = doc.paragraphs # Get a list of all Paragraph objects
    num_paragraphs = len(paragraphs)
    indices_to_remove = [] # Store indices of paragraphs to be removed

    print(f"   - Analyzing {num_paragraphs} paragraphs...")

    # --- Pass 1: Identify paragraphs to remove ---
    for i, current_para in enumerate(paragraphs):
        current_text_stripped = current_para.text.strip()

        # Check if the current paragraph *might* be the one to remove
        if QCM_QCS_PATTERN.match(current_text_stripped):
            # Check context requires looking at surrounding paragraphs
            if i > 0 and i < (num_paragraphs - 1): # Need 1 before, 1 after
                try:
                    para_minus_1_text = paragraphs[i-1].text.strip()
                    para_plus_1_text = paragraphs[i+1].text.strip()

                    # Condition 1: Paragraph i-1 contains a year?
                    has_year_before = bool(YEAR_PATTERN.search(para_minus_1_text))
                    # Condition 2: Paragraph i+1 starts like a question?
                    is_question_after = bool(QUESTION_PATTERN.search(para_plus_1_text))

                    # --- Decision ---
                    if has_year_before and is_question_after:
                        # Mark this index for removal
                        indices_to_remove.append(i)
                        print(f"   - Marking paragraph {i+1} for removal: '{current_text_stripped}'")

                except IndexError:
                    # Should not happen with the boundary check, but good practice
                    print(f"   - Warning: Index boundary issue near paragraph {i+1}.")
                    continue # Skip to next paragraph


    # --- Pass 2: Remove marked paragraphs (in reverse order!) ---
    if indices_to_remove:
        print(f"   - Removing {len(indices_to_remove)} identified paragraph(s)...")
        # Iterate in reverse to avoid index shifting issues after removal
        for index in sorted(indices_to_remove, reverse=True):
            para_to_remove = paragraphs[index]
            remove_paragraph(para_to_remove)
        return True # Indicate that changes were made
    else:
        print("   - No paragraphs matched the removal criteria.")
        return False # No changes made

# --- Main Function ---
def modify_docx_files_in_place():
    """
    Opens a file dialog to select multiple DOCX files, then modifies them
    in place based on the strict context rules for QCM/QCS removal.
    """
    root = tk.Tk()
    root.withdraw()

    file_paths = filedialog.askopenfilenames(
        title="Select DOCX Files to Modify In-Place",
        filetypes=[("Word Documents", "*.docx"), ("All files", "*.*")]
    )

    if not file_paths:
        print("No files selected. Exiting.")
        return

    print(f"Selected {len(file_paths)} file(s).")
    print("--- Starting DOCX processing ---")
    print("!!! WARNING: Files will be modified directly. Ensure you have backups !!!")

    processed_count = 0
    error_count = 0

    for file_path in file_paths:
        print(f"\nProcessing: {file_path}")
        try:
            # 1. Open the DOCX file
            document = docx.Document(file_path)

            # 2. Process the document object
            changes_made = process_docx_document(document)

            # 3. Save the document back ONLY if changes were made
            if changes_made:
                document.save(file_path)
                print(f"   -> Successfully modified and saved.")
                processed_count += 1
            else:
                # No need to save if no changes occurred
                print(f"   -> No modifications needed.")
                # Increment processed count even if no change, as it was analyzed
                processed_count += 1


        except FileNotFoundError:
            print(f"   -> Error: File not found. Skipping.")
            error_count += 1
        except Exception as e:
            # Catch potential errors during docx parsing or saving
            print(f"   -> An unexpected error occurred: {e}")
            error_count += 1

    print("\n--- Processing Complete ---")
    print(f"Files successfully analyzed/modified: {processed_count}")
    print(f"Files skipped due to errors: {error_count}")

# --- Run the script ---
if __name__ == "__main__":
    modify_docx_files_in_place()