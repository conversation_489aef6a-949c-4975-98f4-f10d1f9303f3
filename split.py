import os
import re
import sys
import argparse

def split_text_file(input_file, target_lines=1000, separator_pattern=r"\*{10}[^*]+\*{10}", output_prefix="part_"):
    """
    Split a text file into multiple parts based on a separator pattern.
    Each part will contain approximately target_lines lines, but the split
    will happen at the nearest separator.

    Args:
        input_file (str): Path to the input text file
        target_lines (int): Target number of lines per part
        separator_pattern (str): Regular expression pattern for the separator
        output_prefix (str): Prefix for output files

    Returns:
        list: List of created output files
    """
    try:
        # Read the input file
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        total_lines = len(lines)
        print(f"Total lines in file: {total_lines}")

        # Find all separator positions
        separator_positions = []
        for i, line in enumerate(lines):
            if re.match(separator_pattern, line.strip()):
                separator_positions.append(i)
                print(f"Found separator at line {i+1}: {line.strip()}")

        if not separator_positions:
            print(f"Warning: No separators found using pattern '{separator_pattern}'")
            print("The file will not be split. Please check your separator pattern.")
            return []

        print(f"Found {len(separator_positions)} separators")

        # Calculate the number of parts needed
        num_parts = max(1, (total_lines + target_lines - 1) // target_lines)  # Ceiling division
        print(f"Splitting into approximately {num_parts} parts")

        # Calculate ideal split positions
        ideal_splits = []
        for i in range(1, num_parts):
            ideal_splits.append(i * target_lines)

        # Find the closest separator to each ideal split position
        actual_splits = []
        for ideal_pos in ideal_splits:
            closest_sep = min(separator_positions, key=lambda x: abs(x - ideal_pos))
            actual_splits.append(closest_sep)

        # Remove duplicates and sort
        actual_splits = sorted(list(set(actual_splits)))
        print(f"Will split at lines: {[pos+1 for pos in actual_splits]}")

        # Create output directory if it doesn't exist
        output_dir = os.path.dirname(input_file)
        if not output_dir:
            output_dir = '.'

        created_files = []

        # Split the file and write parts
        start_line = 0
        # If the first separator is not at the beginning of the file, start from the first separator
        if separator_positions and separator_positions[0] > 0 and 0 not in actual_splits:
            start_line = separator_positions[0]
            print(f"Starting from the first separator at line {start_line+1}")

        for i, end_line in enumerate(actual_splits, 1):
            output_file = os.path.join(output_dir, f"{output_prefix}{i}.txt")
            with open(output_file, 'w', encoding='utf-8') as f:
                f.writelines(lines[start_line:end_line])
            print(f"Created {output_file} with {end_line - start_line} lines")
            created_files.append(output_file)
            start_line = end_line

        # Write the last part
        output_file = os.path.join(output_dir, f"{output_prefix}{len(actual_splits) + 1}.txt")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.writelines(lines[start_line:])
        print(f"Created {output_file} with {total_lines - start_line} lines")
        created_files.append(output_file)

        return created_files

    except FileNotFoundError:
        print(f"Error: File '{input_file}' not found.")
        return []
    except Exception as e:
        print(f"Error: {str(e)}")
        return []

def main():
    parser = argparse.ArgumentParser(description='Split a text file into multiple parts based on a separator pattern.')
    parser.add_argument('input_file', help='Path to the input text file')
    parser.add_argument('-l', '--lines', type=int, default=1000,
                        help='Target number of lines per part (default: 1000)')
    parser.add_argument('-s', '--separator', default=r"\*{10}[^*]+\*{10}",
                        help='Regular expression pattern for the separator (default: "\\*{10}[^*]+\\*{10}")')
    parser.add_argument('-p', '--prefix', default="part_",
                        help='Prefix for output files (default: "part_")')

    args = parser.parse_args()

    split_text_file(args.input_file, args.lines, args.separator, args.prefix)

if __name__ == "__main__":
    main()
