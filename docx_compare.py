import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import difflib
import re
from datetime import datetime

class DocxCompareApp:
    def __init__(self, root):
        self.root = root
        self.root.title("DOCX Filename Comparison Tool")
        self.root.geometry("1200x700")

        # Data storage
        self.folders = []  # List of selected folder paths
        self.reference_folder_index = None  # Index of the reference folder in self.folders
        self.docx_files = {}  # Dictionary mapping folder paths to lists of DOCX filenames
        self.comparison_results = {}  # Store comparison results
        self.similarity_threshold = 0.75  # Default similarity threshold

        # Create the main UI
        self.create_ui()

    def create_ui(self):
        """Create the main user interface."""
        # Create a frame for folder selection
        folder_frame = ttk.LabelFrame(self.root, text="Folder Selection")
        folder_frame.pack(fill="x", padx=10, pady=5)

        # Buttons for folder selection
        ttk.Button(folder_frame, text="Add Folder", command=self.add_folder).pack(side="left", padx=5, pady=5)
        ttk.Button(folder_frame, text="Clear Folders", command=self.clear_folders).pack(side="left", padx=5, pady=5)

        # Similarity threshold slider
        threshold_frame = ttk.Frame(folder_frame)
        threshold_frame.pack(side="right", padx=5, pady=5)
        ttk.Label(threshold_frame, text="Similarity Threshold:").pack(side="left")
        self.threshold_var = tk.DoubleVar(value=self.similarity_threshold)
        threshold_slider = ttk.Scale(threshold_frame, from_=0.1, to=1.0, orient="horizontal",
                                    variable=self.threshold_var, length=150)
        threshold_slider.pack(side="left")
        threshold_slider.bind("<ButtonRelease-1>", lambda _: self.update_threshold())
        ttk.Label(threshold_frame, textvariable=tk.StringVar(value=f"{self.similarity_threshold:.2f}")).pack(side="left")

        # Frame for selected folders list
        folders_list_frame = ttk.LabelFrame(self.root, text="Selected Folders")
        folders_list_frame.pack(fill="x", padx=10, pady=5)

        # Listbox for selected folders
        self.folders_listbox = tk.Listbox(folders_list_frame, height=5)
        self.folders_listbox.pack(fill="x", padx=5, pady=5)

        # Reference folder selection
        ref_frame = ttk.Frame(folders_list_frame)
        ref_frame.pack(fill="x", padx=5, pady=5)
        ttk.Label(ref_frame, text="Reference Folder:").pack(side="left")
        self.ref_folder_var = tk.StringVar(value="None")
        ttk.Label(ref_frame, textvariable=self.ref_folder_var).pack(side="left", padx=5)
        ttk.Button(ref_frame, text="Set Selected as Reference",
                  command=self.set_reference_folder).pack(side="left", padx=5)

        # Buttons for comparison
        button_frame = ttk.Frame(self.root)
        button_frame.pack(fill="x", padx=10, pady=5)
        ttk.Button(button_frame, text="Compare Folders", command=self.compare_folders).pack(side="left", padx=5, pady=5)
        self.find_similar_btn = ttk.Button(button_frame, text="Find Similar Files",
                                          command=self.find_similar_files, state="disabled")
        self.find_similar_btn.pack(side="left", padx=5, pady=5)
        ttk.Button(button_frame, text="Export Results", command=self.export_results).pack(side="left", padx=5, pady=5)
        self.rename_files_btn = ttk.Button(button_frame, text="Rename Selected Files",
                                       command=self.rename_selected_files, state="disabled")
        self.rename_files_btn.pack(side="left", padx=5, pady=5)

        # Results notebook (tabbed interface)
        self.results_notebook = ttk.Notebook(self.root)
        self.results_notebook.pack(fill="both", expand=True, padx=10, pady=5)

        # Tab for missing files
        self.missing_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(self.missing_frame, text="Missing Files")

        # Tab for matching files (exact and similar)
        self.matching_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(self.matching_frame, text="Matching Files")

        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief="sunken", anchor="w")
        status_bar.pack(fill="x", padx=10, pady=2, side="bottom")

    def add_folder(self):
        """Open a dialog to select a folder and add it to the list."""
        folder_path = filedialog.askdirectory(title="Select Folder Containing DOCX Files")
        if folder_path:
            # Check if folder already exists in the list
            if folder_path in self.folders:
                messagebox.showwarning("Duplicate Folder", "This folder is already in the list.")
                return

            # Add folder to the list and update UI
            self.folders.append(folder_path)
            self.update_folders_listbox()

            # Scan for DOCX files in the folder
            self.scan_folder(folder_path)

    def clear_folders(self):
        """Clear all selected folders."""
        self.folders = []
        self.docx_files = {}
        self.reference_folder_index = None
        self.ref_folder_var.set("None")
        self.update_folders_listbox()
        self.clear_results()

    def update_folders_listbox(self):
        """Update the listbox displaying selected folders."""
        self.folders_listbox.delete(0, tk.END)
        for i, folder in enumerate(self.folders):
            display_name = os.path.basename(folder)
            if i == self.reference_folder_index:
                display_name += " (Reference)"
            self.folders_listbox.insert(tk.END, f"{display_name} - {folder}")

    def set_reference_folder(self):
        """Set the selected folder as the reference folder."""
        selected_indices = self.folders_listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("No Selection", "Please select a folder to set as reference.")
            return

        self.reference_folder_index = selected_indices[0]
        self.ref_folder_var.set(os.path.basename(self.folders[self.reference_folder_index]))
        self.update_folders_listbox()

    def scan_folder(self, folder_path):
        """Scan a folder for DOCX files and store the filenames."""
        try:
            # Get all files in the folder
            all_files = os.listdir(folder_path)

            # Filter for DOCX files
            docx_files = [f for f in all_files if f.lower().endswith('.docx')]

            # Store the list of DOCX files for this folder
            self.docx_files[folder_path] = docx_files

            self.status_var.set(f"Found {len(docx_files)} DOCX files in {os.path.basename(folder_path)}")
        except Exception as e:
            messagebox.showerror("Error", f"Error scanning folder: {str(e)}")

    def update_threshold(self):
        """Update the similarity threshold when the slider is moved."""
        self.similarity_threshold = round(self.threshold_var.get(), 2)
        # If we have comparison results, refresh them with the new threshold
        if self.comparison_results:
            self.compare_folders()

    def compare_folders(self):
        """Compare DOCX filenames between selected folders to find missing and exact matches."""
        if len(self.folders) < 2:
            messagebox.showwarning("Not Enough Folders", "Please select at least two folders to compare.")
            return

        self.status_var.set("Comparing folders...")
        self.root.update()

        # Clear previous results
        self.clear_results()

        # Determine which folder to use as reference
        reference_folder = self.folders[self.reference_folder_index] if self.reference_folder_index is not None else self.folders[0]

        # Initialize comparison results
        self.comparison_results = {
            'missing': {},    # Files missing from each folder
            'exact': {}       # Exact matches between folders
        }

        # For each folder (except reference), find missing and exact match files
        for folder in self.folders:
            if folder == reference_folder:
                continue

            # Files in reference folder but not in this folder
            missing_files = []
            exact_matches = []

            # Helper function to check if two filenames differ only by the case of the first letter
            def is_first_letter_case_match(file1, file2):
                if len(file1) != len(file2):
                    return False

                # Check if only the first letter differs by case
                if file1[0].lower() == file2[0].lower() and file1[0] != file2[0] and file1[1:] == file2[1:]:
                    return True
                return False

            for ref_file in self.docx_files[reference_folder]:
                # Check for exact match or first-letter-case-only difference
                exact_match_found = False

                # Direct exact match
                if ref_file in self.docx_files[folder]:
                    exact_matches.append(ref_file)
                    exact_match_found = True
                else:
                    # Check for first-letter-case-only difference
                    for other_file in self.docx_files[folder]:
                        if is_first_letter_case_match(ref_file, other_file):
                            # Found a match that differs only by first letter case
                            exact_matches.append(f"{ref_file} (matches {other_file})")
                            exact_match_found = True
                            break

                # If no exact match was found, add to missing files
                if not exact_match_found:
                    missing_files.append(ref_file)

            # Store missing files and exact matches
            self.comparison_results['missing'][folder] = missing_files
            self.comparison_results['exact'][folder] = exact_matches

        # Display results
        self.display_results()

        # Enable the Find Similar Files button if we have results
        self.find_similar_btn.config(state="normal")

        self.status_var.set("Comparison complete")

    def find_similar_files(self, silent=False):
        """Find files with similar names based on the similarity threshold."""
        if not self.comparison_results:
            if not silent:
                messagebox.showwarning("No Comparison Results", "Please compare folders first.")
            return

        self.status_var.set("Finding similar files...")
        self.root.update()

        # Determine which folder to use as reference
        reference_folder = self.folders[self.reference_folder_index] if self.reference_folder_index is not None else self.folders[0]

        # Initialize similar files results if not already present
        if 'similar' not in self.comparison_results:
            self.comparison_results['similar'] = {}

        # For each folder (except reference), find similar filenames
        for folder in self.folders:
            if folder == reference_folder:
                continue

            # Helper function to check if two filenames differ only by the case of the first letter
            def is_first_letter_case_match(file1, file2):
                if len(file1) != len(file2):
                    return False

                # Check if only the first letter differs by case
                if file1[0].lower() == file2[0].lower() and file1[0] != file2[0] and file1[1:] == file2[1:]:
                    return True
                return False

            # Find similar filenames (for missing files only)
            similar_files = []
            for ref_file in self.comparison_results['missing'].get(folder, []):
                # Find potential matches based on similarity
                matches = []
                for other_file in self.docx_files[folder]:
                    # Skip files that would be considered exact matches due to first letter case
                    if is_first_letter_case_match(ref_file, other_file):
                        continue

                    similarity = difflib.SequenceMatcher(None, ref_file, other_file).ratio()
                    if similarity >= self.similarity_threshold:
                        matches.append((other_file, similarity))

                # Sort matches by similarity (highest first)
                matches.sort(key=lambda x: x[1], reverse=True)

                if matches:
                    similar_files.append((ref_file, matches))

            # Store similar files
            self.comparison_results['similar'][folder] = similar_files

        # Update the display to show similar files
        self.display_similar_results()

        # Enable the Rename Selected Files button if we have similar files
        has_similar = any(similar for similar in self.comparison_results['similar'].values())
        self.rename_files_btn.config(state="normal" if has_similar else "disabled")

        self.status_var.set("Similar file search complete")

    def display_results(self):
        """Display missing files and exact matches in the UI."""
        # Clear previous results
        for widget in self.missing_frame.winfo_children():
            widget.destroy()
        for widget in self.matching_frame.winfo_children():
            widget.destroy()

        # Determine reference folder
        reference_folder = self.folders[self.reference_folder_index] if self.reference_folder_index is not None else self.folders[0]
        ref_folder_name = os.path.basename(reference_folder)

        # Create scrollable frame for missing files
        missing_canvas = tk.Canvas(self.missing_frame)
        missing_scrollbar = ttk.Scrollbar(self.missing_frame, orient="vertical", command=missing_canvas.yview)
        missing_scrollable_frame = ttk.Frame(missing_canvas)

        missing_scrollable_frame.bind(
            "<Configure>",
            lambda _: missing_canvas.configure(scrollregion=missing_canvas.bbox("all"))
        )

        missing_canvas.create_window((0, 0), window=missing_scrollable_frame, anchor="nw")
        missing_canvas.configure(yscrollcommand=missing_scrollbar.set)

        missing_canvas.pack(side="left", fill="both", expand=True)
        missing_scrollbar.pack(side="right", fill="y")

        # Display missing files
        ttk.Label(missing_scrollable_frame, text=f"Files in reference folder '{ref_folder_name}' missing from other folders:",
                 font=("", 10, "bold")).pack(anchor="w", padx=10, pady=5)

        for folder, missing in self.comparison_results['missing'].items():
            folder_name = os.path.basename(folder)
            ttk.Label(missing_scrollable_frame, text=f"Missing from '{folder_name}':",
                     font=("", 9, "bold")).pack(anchor="w", padx=20, pady=2)

            if not missing:
                ttk.Label(missing_scrollable_frame, text="No missing files").pack(anchor="w", padx=30, pady=2)
            else:
                for file in missing:
                    ttk.Label(missing_scrollable_frame, text=file).pack(anchor="w", padx=30, pady=1)

        # Create scrollable frame for matching files (exact matches)
        matching_canvas = tk.Canvas(self.matching_frame)
        matching_scrollbar = ttk.Scrollbar(self.matching_frame, orient="vertical", command=matching_canvas.yview)
        matching_scrollable_frame = ttk.Frame(matching_canvas)

        matching_scrollable_frame.bind(
            "<Configure>",
            lambda _: matching_canvas.configure(scrollregion=matching_canvas.bbox("all"))
        )

        matching_canvas.create_window((0, 0), window=matching_scrollable_frame, anchor="nw")
        matching_canvas.configure(yscrollcommand=matching_scrollbar.set)

        matching_canvas.pack(side="left", fill="both", expand=True)
        matching_scrollbar.pack(side="right", fill="y")

        # Display exact matches
        ttk.Label(matching_scrollable_frame, text=f"Exact matches between folders:",
                 font=("", 10, "bold")).pack(anchor="w", padx=10, pady=5)

        for folder, exact_matches in self.comparison_results['exact'].items():
            folder_name = os.path.basename(folder)
            ttk.Label(matching_scrollable_frame, text=f"Exact matches in '{folder_name}':",
                     font=("", 9, "bold")).pack(anchor="w", padx=20, pady=2)

            if not exact_matches:
                ttk.Label(matching_scrollable_frame, text="No exact matches").pack(anchor="w", padx=30, pady=2)
            else:
                ttk.Label(matching_scrollable_frame, text=f"Found {len(exact_matches)} exact matches").pack(anchor="w", padx=30, pady=2)
                # Show all matches
                for file in exact_matches:
                    ttk.Label(matching_scrollable_frame, text=file, foreground="green").pack(anchor="w", padx=30, pady=1)

        # Add a note about finding similar files
        ttk.Label(matching_scrollable_frame, text="\nTo find similar files for potential renaming, click the 'Find Similar Files' button.",
                 font=("", 9, "italic")).pack(anchor="w", padx=10, pady=10)

    def display_similar_results(self):
        """Display similar files in the matching tab."""
        # Clear only the similar files section, keeping the exact matches
        # First, save the exact matches section
        exact_matches_widgets = []
        for widget in self.matching_frame.winfo_children():
            exact_matches_widgets.append(widget)

        # Clear the matching frame
        for widget in self.matching_frame.winfo_children():
            widget.destroy()

        # Recreate the scrollable frame for matching files
        matching_canvas = tk.Canvas(self.matching_frame)
        matching_scrollbar = ttk.Scrollbar(self.matching_frame, orient="vertical", command=matching_canvas.yview)
        matching_scrollable_frame = ttk.Frame(matching_canvas)

        matching_scrollable_frame.bind(
            "<Configure>",
            lambda _: matching_canvas.configure(scrollregion=matching_canvas.bbox("all"))
        )

        matching_canvas.create_window((0, 0), window=matching_scrollable_frame, anchor="nw")
        matching_canvas.configure(yscrollcommand=matching_scrollbar.set)

        matching_canvas.pack(side="left", fill="both", expand=True)
        matching_scrollbar.pack(side="right", fill="y")

        # No need to determine reference folder here

        # Display exact matches first
        ttk.Label(matching_scrollable_frame, text=f"Exact matches between folders:",
                 font=("", 10, "bold")).pack(anchor="w", padx=10, pady=5)

        for folder, exact_matches in self.comparison_results['exact'].items():
            folder_name = os.path.basename(folder)
            ttk.Label(matching_scrollable_frame, text=f"Exact matches in '{folder_name}':",
                     font=("", 9, "bold")).pack(anchor="w", padx=20, pady=2)

            if not exact_matches:
                ttk.Label(matching_scrollable_frame, text="No exact matches").pack(anchor="w", padx=30, pady=2)
            else:
                ttk.Label(matching_scrollable_frame, text=f"Found {len(exact_matches)} exact matches").pack(anchor="w", padx=30, pady=2)
                # Show all matches
                for file in exact_matches:
                    ttk.Label(matching_scrollable_frame, text=file, foreground="green").pack(anchor="w", padx=30, pady=1)

        # Display similar files
        ttk.Label(matching_scrollable_frame, text=f"\nFiles with similar names (similarity ≥ {self.similarity_threshold:.2f}):",
                 font=("", 10, "bold")).pack(anchor="w", padx=10, pady=5)

        has_similar = False
        for folder, similar in self.comparison_results['similar'].items():
            folder_name = os.path.basename(folder)
            ttk.Label(matching_scrollable_frame, text=f"Similar files in '{folder_name}':",
                     font=("", 9, "bold")).pack(anchor="w", padx=20, pady=2)

            if not similar:
                ttk.Label(matching_scrollable_frame, text="No similar files").pack(anchor="w", padx=30, pady=2)
            else:
                has_similar = True
                for ref_file, matches in similar:
                    frame = ttk.Frame(matching_scrollable_frame)
                    frame.pack(fill="x", padx=30, pady=5)

                    ttk.Label(frame, text=f"Reference: {ref_file}", foreground="blue").pack(anchor="w")

                    for match_file, similarity in matches:
                        match_frame = ttk.Frame(frame)
                        match_frame.pack(fill="x", padx=10, pady=2)

                        ttk.Label(match_frame, text=f"Match: {match_file} (Similarity: {similarity:.2f})").pack(side="left")

                        # Add checkbox for selecting this match for renaming
                        var = tk.BooleanVar(value=False)
                        ttk.Checkbutton(match_frame, text="Select for rename", variable=var).pack(side="right")

                        # Store the reference to this checkbox variable for later use
                        if not hasattr(self, 'rename_selections'):
                            self.rename_selections = {}

                        if folder not in self.rename_selections:
                            self.rename_selections[folder] = {}

                        if ref_file not in self.rename_selections[folder]:
                            self.rename_selections[folder][ref_file] = {}

                        self.rename_selections[folder][ref_file][match_file] = var

        # Add a note about renaming files
        if has_similar:
            ttk.Label(matching_scrollable_frame, text="\nSelect files to rename and click 'Rename Selected Files'.",
                    font=("", 9, "italic")).pack(anchor="w", padx=10, pady=10)
        else:
            ttk.Label(matching_scrollable_frame, text="\nNo similar files found. Try lowering the similarity threshold.",
                    font=("", 9, "italic")).pack(anchor="w", padx=10, pady=10)

    def clear_results(self):
        """Clear all comparison results."""
        self.comparison_results = {}
        for widget in self.missing_frame.winfo_children():
            widget.destroy()
        for widget in self.matching_frame.winfo_children():
            widget.destroy()
        if hasattr(self, 'rename_selections'):
            del self.rename_selections

        # Reset button states
        self.find_similar_btn.config(state="disabled")
        self.rename_files_btn.config(state="disabled")

    def export_results(self):
        """Export comparison results to a text file."""
        if not self.comparison_results:
            messagebox.showwarning("No Results", "No comparison results to export.")
            return

        # Ask for save location
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            title="Save Comparison Results"
        )

        if not file_path:
            return

        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                # Write header
                f.write("DOCX Filename Comparison Results\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                # Write folder information
                f.write("Folders compared:\n")
                for i, folder in enumerate(self.folders):
                    ref_marker = " (Reference)" if i == self.reference_folder_index else ""
                    f.write(f"  {i+1}. {folder}{ref_marker}\n")

                f.write(f"\nSimilarity threshold: {self.similarity_threshold:.2f}\n\n")

                # Determine reference folder
                reference_folder = self.folders[self.reference_folder_index] if self.reference_folder_index is not None else self.folders[0]
                ref_folder_name = os.path.basename(reference_folder)

                # Write missing files
                f.write(f"Files in reference folder '{ref_folder_name}' missing from other folders:\n")
                for folder, missing in self.comparison_results['missing'].items():
                    folder_name = os.path.basename(folder)
                    f.write(f"\nMissing from '{folder_name}':\n")

                    if not missing:
                        f.write("  No missing files\n")
                    else:
                        for file in missing:
                            f.write(f"  {file}\n")

                # Write exact matches
                f.write(f"\n\nExact matches between folders:\n")
                for folder, exact_matches in self.comparison_results['exact'].items():
                    folder_name = os.path.basename(folder)
                    f.write(f"\nExact matches in '{folder_name}':\n")

                    if not exact_matches:
                        f.write("  No exact matches\n")
                    else:
                        f.write(f"  Found {len(exact_matches)} exact matches\n")
                        # List all matches
                        for file in exact_matches:
                            f.write(f"  {file}\n")

                # Write similar files if they exist
                if 'similar' in self.comparison_results:
                    f.write(f"\n\nFiles with similar names (similarity ≥ {self.similarity_threshold:.2f}):\n")
                    for folder, similar in self.comparison_results['similar'].items():
                        folder_name = os.path.basename(folder)
                        f.write(f"\nSimilar files in '{folder_name}':\n")

                        if not similar:
                            f.write("  No similar files\n")
                        else:
                            for ref_file, matches in similar:
                                f.write(f"\n  Reference: {ref_file}\n")
                                for match_file, similarity in matches:
                                    f.write(f"    Match: {match_file} (Similarity: {similarity:.2f})\n")
                else:
                    f.write("\n\nSimilar files have not been searched for yet. Use the 'Find Similar Files' button to search for similar files.\n")

            self.status_var.set(f"Results exported to {os.path.basename(file_path)}")
            messagebox.showinfo("Export Complete", f"Results exported to {file_path}")

        except Exception as e:
            messagebox.showerror("Export Error", f"Error exporting results: {str(e)}")

    def rename_selected_files(self):
        """Rename selected files directly."""
        if not hasattr(self, 'rename_selections') or not self.rename_selections:
            messagebox.showwarning("No Selections", "No files selected for renaming.")
            return

        # Confirm before proceeding
        confirm = messagebox.askyesno(
            "Confirm Rename",
            "This will rename the selected files directly. Continue?",
            icon="warning"
        )

        if not confirm:
            return

        try:
            # Collect all rename operations
            rename_operations = []

            for folder_path, ref_files in self.rename_selections.items():
                for ref_file, matches in ref_files.items():
                    for match_file, selected in matches.items():
                        if selected.get():
                            # Create full paths
                            old_path = os.path.join(folder_path, match_file)
                            new_path = os.path.join(folder_path, ref_file)

                            # Check if source file exists
                            if not os.path.exists(old_path):
                                messagebox.showwarning(
                                    "File Not Found",
                                    f"Source file not found: {old_path}\nSkipping this rename operation."
                                )
                                continue

                            # Check if target file already exists
                            if os.path.exists(new_path):
                                overwrite = messagebox.askyesno(
                                    "File Exists",
                                    f"Target file already exists: {new_path}\nOverwrite?",
                                    icon="warning"
                                )
                                if not overwrite:
                                    continue

                            # Add to operations list
                            rename_operations.append((old_path, new_path))

            if not rename_operations:
                messagebox.showinfo("No Files", "No valid files selected for renaming.")
                return

            # Perform the rename operations
            success_count = 0
            error_count = 0
            error_messages = []

            for old_path, new_path in rename_operations:
                try:
                    # If target exists and we got here, user confirmed overwrite
                    if os.path.exists(new_path):
                        # Create a backup with .bak extension
                        backup_path = f"{new_path}.bak"
                        os.rename(new_path, backup_path)

                    # Rename the file
                    os.rename(old_path, new_path)
                    success_count += 1
                except Exception as e:
                    error_count += 1
                    error_messages.append(f"Error renaming {os.path.basename(old_path)}: {str(e)}")

            # Show results
            if error_count == 0:
                messagebox.showinfo("Rename Complete", f"Successfully renamed {success_count} files.")
            else:
                error_details = "\n".join(error_messages[:5])
                if len(error_messages) > 5:
                    error_details += f"\n... and {len(error_messages) - 5} more errors."

                messagebox.showwarning(
                    "Rename Partial",
                    f"Renamed {success_count} files with {error_count} errors.\n\nErrors:\n{error_details}"
                )

            # Update status
            self.status_var.set(f"Renamed {success_count} files ({error_count} errors)")

            # Refresh the folders to show the new filenames
            self.refresh_folders()

        except Exception as e:
            messagebox.showerror("Error", f"Error during rename operation: {str(e)}")

    def refresh_folders(self):
        """Refresh the folder contents after renaming files."""
        # Re-scan all folders
        for folder in self.folders:
            self.scan_folder(folder)

        # Re-run the comparison
        self.compare_folders()

        # If we had found similar files before, find them again
        if 'similar' in self.comparison_results:
            self.find_similar_files(silent=True)

# Main application entry point
if __name__ == "__main__":
    root = tk.Tk()
    app = DocxCompareApp(root)
    root.mainloop()
