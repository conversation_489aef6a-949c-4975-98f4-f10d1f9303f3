import re
import tkinter as tk
from tkinter import filedialog
import io

# --- Reg<PERSON> Patterns ---
# Matches a line containing 20 or more '#' or '*' characters
SEPARATOR_PATTERN = re.compile(r"^[#*]{20,}$")
# Finds a plausible 4-digit year (19xx or 20xx) anywhere in the line
YEAR_PATTERN = re.compile(r"\b(19|20)\d{2}\b")
# Matches a line that is exactly 'QCM' or 'QCS' (case-insensitive)
QCM_QCS_PATTERN = re.compile(r"^(QCM|QCS)$", re.IGNORECASE)
# Matches a line starting with 'Q' followed by digits OR digits followed by '.'
# Used search() later, so it doesn't have to be the *only* thing on the line
QUESTION_PATTERN = re.compile(r"^(Q\d+|\d+\.)")

def process_content(lines):
    """
    Processes a list of lines to remove QCM/QCS lines only if they appear
    in the specific sequence: Separator -> Year Line -> QCM/QCS -> Question Line.

    Args:
        lines (list): A list of strings, each representing a line from the file.

    Returns:
        list: A list of strings representing the modified content.
    """
    processed_lines = []
    num_lines = len(lines)

    # Iterate through the lines using index
    for i, current_line in enumerate(lines):
        current_line_stripped = current_line.strip()
        should_remove = False # Assume we keep the line by default

        # --- Check if the current line is potentially removable (QCM/QCS) ---
        if QCM_QCS_PATTERN.match(current_line_stripped):
            # We only proceed if the current line IS QCM/QCS. Now check context.

            # --- Check Context ---
            # Need at least 2 lines before and 1 line after for full context check
            if i > 1 and i < (num_lines - 1):
                line_minus_2_stripped = lines[i-2].strip()
                line_minus_1_stripped = lines[i-1].strip()
                line_plus_1_stripped = lines[i+1].strip()

                # Condition 1: Line i-2 is a separator?
                is_separator_before = bool(SEPARATOR_PATTERN.match(line_minus_2_stripped))
                # Condition 2: Line i-1 contains a year?
                has_year_before = bool(YEAR_PATTERN.search(line_minus_1_stripped))
                # Condition 3: Line i+1 starts like a question?
                is_question_after = bool(QUESTION_PATTERN.search(line_plus_1_stripped)) # Use search

                # --- Decision ---
                if is_separator_before and has_year_before and is_question_after:
                    # Only if ALL context conditions are met, mark for removal
                    should_remove = True
                    print(f"   - Removing line {i+1}: '{current_line_stripped}' (Context matched)")

        # --- Append line if not marked for removal ---
        if not should_remove:
            processed_lines.append(current_line) # Append the original line with newline

    return processed_lines

def modify_files_in_place():
    """
    Opens a file dialog to select multiple text files, then modifies them
    in place based on the stricter context rules for QCM/QCS removal.
    """
    # Set up tkinter root window (it won't be shown)
    root = tk.Tk()
    root.withdraw() # Hide the root window

    # Open file dialog to select multiple files
    file_paths = filedialog.askopenfilenames(
        title="Select Text Files to Modify In-Place (Strict Rules)",
        filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
    )

    if not file_paths:
        print("No files selected. Exiting.")
        return

    print(f"Selected {len(file_paths)} file(s).")
    print("--- Starting processing (Strict Rules) ---")
    print("!!! WARNING: Files will be modified directly. Ensure you have backups !!!")

    processed_count = 0
    error_count = 0

    for file_path in file_paths:
        print(f"\nProcessing: {file_path}")
        original_lines = [] # Ensure it's defined even if reading fails
        try:
            # 1. Read the entire original file content into memory
            with open(file_path, 'r', encoding='utf-8') as infile:
                original_lines = infile.readlines()

            # 2. Process the content in memory using the stricter logic
            modified_lines = process_content(original_lines)

            # 3. Check if modifications were actually made
            if len(original_lines) == len(modified_lines):
                 print("   - No targeted QCM/QCS lines removed based on strict context.")
                 # Optionally skip writing if no changes detected
                 # continue # Uncomment this line if you ONLY want to write if changes occurred

            # 4. Overwrite the original file ONLY if modifications were intended
            elif len(modified_lines) < len(original_lines):
                print(f"   -> Writing {len(modified_lines)} lines back (removed {len(original_lines) - len(modified_lines)}).")
                with open(file_path, 'w', encoding='utf-8') as outfile:
                    outfile.writelines(modified_lines)
                processed_count += 1
            else:
                 # This case shouldn't happen with removal logic, but good practice
                 print("   - Analysis complete, no lines removed.")


        except FileNotFoundError:
            print(f"   -> Error: File not found (maybe deleted after selection?). Skipping.")
            error_count += 1
        except Exception as e:
            print(f"   -> An unexpected error occurred during processing or writing: {e}")
            # Avoid writing back potentially corrupted data if error happened mid-process
            error_count += 1

    print("\n--- Processing Complete ---")
    print(f"Files successfully modified: {processed_count}")
    print(f"Files skipped or errored: {error_count}")

# --- Run the main function ---
if __name__ == "__main__":
    modify_files_in_place()