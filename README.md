# Text File Splitter

This Python script splits a text file into multiple parts based on a specified separator pattern. Each part will contain approximately a target number of lines, but the split will happen at the nearest separator to ensure that each part starts with a separator.

## Features

- Split text files into multiple parts based on a separator pattern
- Ensure each part starts with a separator
- Each part will contain approximately the target number of lines (default: 1000)
- Customize the target number of lines per part
- Customize the output file prefix
- Detailed output information
- If the first separator is not at the beginning of the file, the script will start from the first separator

## Usage

```
python split_text_file.py <input_file> [options]
```

### Options

- `-l, --lines`: Target number of lines per part (default: 1000)
- `-s, --separator`: Regular expression pattern for the separator (default: "\*{10}[^*]+\*{10}")
- `-p, --prefix`: Prefix for output files (default: "part_")

### Examples

Split a file into parts of approximately 500 lines each:
```
python split_text_file.py myfile.txt -l 500
```

Use a custom separator pattern:
```
python split_text_file.py myfile.txt -s "^Chapter [0-9]+"
```

Customize the output file prefix:
```
python split_text_file.py myfile.txt -p "chapter_"
```

## How It Works

1. The script reads the input file and identifies all occurrences of the separator pattern.
2. It calculates the ideal split positions based on the target number of lines.
3. For each ideal split position, it finds the closest separator.
4. It splits the file at these separator positions and writes each part to a separate file.

## Requirements

- Python 3.6 or higher

## License

This project is licensed under the MIT License - see the LICENSE file for details.
