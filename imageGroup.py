import os
import re
import shutil
import tkinter as tk
from tkinter import filedialog

# --- Configuration ---
# Pattern to identify the base name and number.
# Assumes format "Base Name qst Number.extension"
# It splits the filename (without extension) from the RIGHT at the last " qst "
SPLIT_KEYWORD = " qst "
NEW_FILENAME_PREFIX = "QCM"
# --- End Configuration ---

def organize_files(source_dir):
    """
    Organizes files from the source directory into subdirectories based on
    their names, renaming them sequentially within each subdirectory.
    """
    if not os.path.isdir(source_dir):
        print(f"Error: Invalid source directory provided: '{source_dir}'")
        return

    files_to_process = []
    print(f"Scanning directory: {source_dir}")

    # --- Step 1: Scan and Parse Filenames ---
    for filename in os.listdir(source_dir):
        original_filepath = os.path.join(source_dir, filename)

        # Skip directories, process only files
        if not os.path.isfile(original_filepath):
            continue

        # Split filename from extension
        base_name_full, extension = os.path.splitext(filename)

        # Try to split based on the keyword from the right
        try:
            # rsplit ensures we split on the *last* occurrence if the keyword appears multiple times
            group_name_raw, original_num_str = base_name_full.rsplit(SPLIT_KEYWORD, 1)
            group_name = group_name_raw.strip() # Remove leading/trailing whitespace
            original_num = int(original_num_str.strip()) # Convert number part to integer for sorting
            files_to_process.append({
                "original_filename": filename,
                "group_name": group_name,
                "original_num": original_num,
                "extension": extension
            })
        except ValueError:
            print(f"  - Skipping '{filename}': Could not parse using '{SPLIT_KEYWORD}'.")
            continue # Skip files that don't match the pattern

    # --- Step 2: Sort Files ---
    # Sort primarily by group name, secondarily by original number
    files_to_process.sort(key=lambda x: (x['group_name'], x['original_num']))

    # --- Step 3 & 4: Create Folders, Rename and Move ---
    group_counters = {} # To keep track of QCM numbers for each group

    print("\nProcessing and moving files...")
    moved_count = 0
    for file_info in files_to_process:
        group_name = file_info['group_name']
        original_filename = file_info['original_filename']
        extension = file_info['extension']

        # Determine target directory
        target_dir = os.path.join(source_dir, group_name)

        # Create target directory if it doesn't exist
        os.makedirs(target_dir, exist_ok=True) # exist_ok=True prevents error if dir exists

        # Get the next sequential number for this group
        current_count = group_counters.get(group_name, 0) + 1
        group_counters[group_name] = current_count

        # Format the new filename (e.g., QCM01, QCM02, etc.)
        new_filename = f"{NEW_FILENAME_PREFIX}{current_count:02d}{extension}" # :02d ensures two digits with leading zero

        # Construct full old and new paths
        old_filepath = os.path.join(source_dir, original_filename)
        new_filepath = os.path.join(target_dir, new_filename)

        # Move and rename the file
        try:
            print(f"  - Moving '{original_filename}' to '{os.path.join(group_name, new_filename)}'")
            # os.rename can move files across directories on the same filesystem
            os.rename(old_filepath, new_filepath)
            moved_count += 1
            # Alternatively, use shutil.move for potentially more robustness (e.g., across drives)
            # shutil.move(old_filepath, new_filepath)
        except OSError as e:
            print(f"    Error moving file '{original_filename}': {e}")

    if moved_count > 0:
        print(f"\nSuccessfully processed and moved {moved_count} file(s).")
    else:
        print("\nNo files matching the pattern were found or moved.")
    print("Organization complete.")

# --- Main Execution ---
if __name__ == "__main__":
    # Set up the Tkinter root window (it won't be shown)
    root = tk.Tk()
    root.withdraw() # Hide the main window

    print("Please select the folder containing your image files.")

    # Open the directory selection dialog
    selected_directory = filedialog.askdirectory(
        title="Select Folder Containing Images"
    )

    # Check if the user selected a directory or cancelled
    if selected_directory:
        print(f"Selected directory: {selected_directory}")
        # Call the organization function with the selected path
        organize_files(selected_directory)
    else:
        print("No directory selected. Exiting.")

    # Keep the console window open until user presses Enter (optional)
    # input("Press Enter to exit...")