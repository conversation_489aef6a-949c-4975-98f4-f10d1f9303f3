import os
import sys
from docx import Document  # Import the Document class from the docx library

def merge_docx_to_txt(input_folder, output_txt_file):
    """
    Merges all DOCX files found in the input_folder into a single TXT file,
    separating content by filename markers.

    Args:
        input_folder (str): The path to the folder containing DOCX files.
        output_txt_file (str): The path/name for the resulting TXT file.
    """
    # --- 1. Validate Input Folder ---
    if not os.path.isdir(input_folder):
        print(f"Error: Input folder '{input_folder}' not found or is not a directory.")
        sys.exit(1) # Exit if the input folder is invalid

    # --- 2. Find DOCX Files ---
    docx_files = []
    print(f"Scanning folder '{input_folder}' for .docx files...")
    for filename in os.listdir(input_folder):
        # Check for .docx extension and ignore temporary Word files (often start with ~$)
        if filename.lower().endswith(".docx") and not filename.startswith('~$'):
            full_path = os.path.join(input_folder, filename)
            if os.path.isfile(full_path): # Make sure it's actually a file
                docx_files.append(full_path)

    if not docx_files:
        print("No .docx files found in the specified folder.")
        return # Nothing to do, so just return

    print(f"Found {len(docx_files)} .docx file(s) to merge.")
    docx_files.sort() # Process files in alphabetical order for consistency

    # --- 3. Process Files and Write to Output ---
    try:
        with open(output_txt_file, 'w', encoding='utf-8') as outfile:
            print(f"Writing merged content to '{output_txt_file}'...")

            for i, docx_path in enumerate(docx_files):
                base_filename = os.path.basename(docx_path)
                # Get filename without extension
                filename_no_ext, _ = os.path.splitext(base_filename)
                print(f"  Processing: {base_filename}")

                try:
                    # --- Write Separator ---
                    # Add a newline before the separator IF it's not the very first file
                    if i > 0:
                       outfile.write('\n')
                    separator = f"**********{filename_no_ext}**********\n"
                    outfile.write(separator)

                    # --- Read DOCX Content ---
                    doc = Document(docx_path)
                    full_text = []
                    for para in doc.paragraphs:
                        full_text.append(para.text)

                    # --- Write Content ---
                    # Join paragraphs with a newline character
                    outfile.write('\n'.join(full_text))
                    # Add a newline after the content before the potential next separator
                    outfile.write('\n')


                except Exception as e:
                    # Handle errors reading a specific file (e.g., corrupted)
                    error_msg = f"  -> Error processing file '{base_filename}': {e}"
                    print(error_msg, file=sys.stderr) # Print error to standard error
                    # Write an error message into the output file as well
                    outfile.write(f"**********ERROR PROCESSING: {filename_no_ext}**********\n")
                    outfile.write(f"Could not read content due to error: {e}\n")

        print("-" * 20)
        print(f"Successfully merged DOCX files into '{output_txt_file}'.")

    except IOError as e:
        print(f"Error: Could not open or write to output file '{output_txt_file}': {e}", file=sys.stderr)
    except Exception as e:
        print(f"An unexpected error occurred during file writing: {e}", file=sys.stderr)

# --- Main execution block ---
if __name__ == "__main__":
    print("--- DOCX to TXT Merger ---")

    # Get input directory from user
    input_dir = input("Enter the path to the folder containing your DOCX files: ").strip()

    # Get output filename from user
    output_file = input("Enter the desired name for the output TXT file (e.g., merged_output.txt): ").strip()

    # Basic validation for output file name
    if not output_file:
        print("Error: Output filename cannot be empty.")
        sys.exit(1)
    # Ensure it ends with .txt for clarity, append if necessary
    if not output_file.lower().endswith(".txt"):
        print("Warning: Output filename doesn't end with .txt. Appending '.txt'.")
        output_file += ".txt"

    # Run the merging function
    merge_docx_to_txt(input_dir, output_file)

    print("--------------------------")
    print("Script finished.")