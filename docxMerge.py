import os
import sys
from docx import Document # Used by docxcompose
from docxcompose.composer import Composer
import tkinter as tk
from tkinter import filedialog

def merge_docx_in_folders(base_dir):
    """
    Merges DOCX files within subfolders of base_dir.

    Expects subfolders named 'FolderName'.
    Looks for DOCX files inside named like 'filename.FolderName.Suffix.docx'.
    Merges them into 'FolderName.Suffix.docx' in the base_dir.
    """
    print(f"Scanning directory: {base_dir}")

    if not os.path.isdir(base_dir):
        print(f"Error: Base directory '{base_dir}' not found or is not a directory.")
        return

    # Iterate through items in the base directory
    for item in os.listdir(base_dir):
        folder_path = os.path.join(base_dir, item)

        # Check if it's a directory
        if os.path.isdir(folder_path):
            folder_name = item # This is our 'X', 'Y', etc.
            print(f"\n--- Processing folder: {folder_name} ---")

            docx_files_to_merge = []
            suffix = None # To store 'Anatomie', 'Biologie', etc.

            # Find all .docx files in this specific folder
            for filename in os.listdir(folder_path):
                file_path = os.path.join(folder_path, filename)
                # Check if it's a docx file and seems to follow the pattern
                if filename.lower().endswith('.docx') and os.path.isfile(file_path):
                     # Try to extract the suffix based on the pattern 'name.FolderName.Suffix.docx'
                    try:
                        parts = filename.split('.')
                        # Expecting at least 4 parts: name, folder, suffix, docx
                        if len(parts) >= 4 and parts[-2] and parts[-3].lower() == folder_name.lower():
                             current_suffix = parts[-2]
                             # If this is the first file, set the expected suffix
                             if suffix is None:
                                 suffix = current_suffix
                                 print(f"Determined suffix for this folder: '{suffix}'")
                                 docx_files_to_merge.append(file_path)
                             # If it's a subsequent file, check if the suffix matches
                             elif current_suffix == suffix:
                                 docx_files_to_merge.append(file_path)
                             else:
                                 print(f"Warning: File '{filename}' has suffix '{current_suffix}' which differs from expected '{suffix}'. Skipping.")
                        else:
                            # Handle cases where filename doesn't fit pattern exactly
                            # Maybe check if '.FolderName.' is present as a fallback?
                             if f'.{folder_name}.' in filename:
                                print(f"Warning: File '{filename}' has an unexpected format but contains '.{folder_name}.'. Attempting pattern match failed. Trying to guess suffix (might be wrong).")
                                # Less reliable suffix guess: take part before '.docx'
                                simple_suffix_guess = parts[-2] if len(parts) >= 2 else None
                                if suffix is None and simple_suffix_guess:
                                    suffix = simple_suffix_guess
                                    print(f"Guessed suffix for this folder: '{suffix}' (verify!)")
                                    docx_files_to_merge.append(file_path)
                                elif simple_suffix_guess == suffix:
                                    docx_files_to_merge.append(file_path)
                                else:
                                     print(f"Warning: Skipping file '{filename}' due to inconsistent naming or inability to determine suffix reliably.")
                             else:
                                print(f"Warning: Skipping file '{filename}' as it doesn't seem to match the pattern 'name.{folder_name}.Suffix.docx'.")


                    except Exception as e:
                        print(f"Warning: Could not parse filename '{filename}'. Error: {e}. Skipping.")


            # --- Merging Phase ---
            if not docx_files_to_merge:
                print(f"No DOCX files matching the pattern found in folder '{folder_name}'.")
                continue # Skip to the next folder

            if suffix is None:
                print(f"Error: Could not determine a consistent suffix for folder '{folder_name}'. Cannot create output file. Skipping.")
                continue

            # Sort files alphabetically to ensure consistent merge order (optional but recommended)
            docx_files_to_merge.sort()
            print(f"Found {len(docx_files_to_merge)} files to merge:")
            for f in docx_files_to_merge:
                print(f"  - {os.path.basename(f)}")

            # Define the output filename
            output_filename = f"{folder_name}.{suffix}.docx"
            output_path = os.path.join(base_dir, output_filename) # Save in the parent directory

            try:
                # Use the first document as the base
                master = Document(docx_files_to_merge[0])
                composer = Composer(master)

                # Append the rest of the documents
                # We start from the second file (index 1)
                for i in range(1, len(docx_files_to_merge)):
                    doc_to_append = Document(docx_files_to_merge[i])
                    # IMPORTANT: Decide if you want page breaks between merged docs
                    # To add a page break:
                    # master.add_page_break()
                    # composer.append handles sections better than manual copying
                    composer.append(doc_to_append)

                # Save the final merged document
                composer.save(output_path)
                print(f"Successfully merged files into: '{output_path}'")

            except Exception as e:
                print(f"Error merging files for folder '{folder_name}': {e}")

    print("\n--- Script finished ---")

# --- How to run ---
if __name__ == "__main__":
    # --- Folder Selection GUI ---
    print("Initializing folder selector...")
    root = tk.Tk()
    root.withdraw() # Hide the annoying empty tkinter window

    print("Please select the main directory containing the folders (e.g., X, Y, Z)...")
    target_directory = filedialog.askdirectory(
        title="Select the Main Directory Containing Your Project Folders"
    )

    # Clean up the tkinter root window
    # Need to check if it still exists in case of errors or immediate closure
    try:
        root.destroy()
    except tk.TclError:
        pass # Window already destroyed

    # --- Validate Selection ---
    if not target_directory: # User cancelled or closed the dialog
        print("No directory selected. Exiting.")
        sys.exit(0) # Exit gracefully without an error

    print(f"\nSelected directory: {target_directory}")

    # --- Run the merge process ---
    # Basic check if the selected path actually exists (it should, if selected via dialog)
    if not os.path.isdir(target_directory):
         print(f"Error: The selected path '{target_directory}' is not a valid directory. Exiting.")
         sys.exit(1)

    merge_docx_in_folders(target_directory)