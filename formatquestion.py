import re
import os
import tkinter as tk
from tkinter import filedialog

# --- Configuration ---
# Define the file extension(s) to look for within the selected folder
# Make it lowercase for case-insensitive matching
ALLOWED_EXTENSIONS = ('.txt', '.md', '.dat') # Add or change extensions as needed

# --- Regular Expressions (same as before) ---
# Matches lines starting with Q, followed by digits, then optional space and separator
# Captures: 1='Q', 2='digits', 3=' optional space and separator like " -" or " ."'
question_regex = re.compile(r"^(Q)(\d+)(\s*[-.])")

# Matches the answer line
# Captures: 1='Réponse : ' part (with variable spacing), 2='answer letters part'
answer_regex = re.compile(r"^(Réponse\s*:\s*)(.*)$")

# --- Processing Function (same as before) ---
def process_file(filepath):
    """Reads a file, applies transformations, and writes back."""
    print(f"Processing file: {filepath}...")
    try:
        # Try common encodings if utf-8 fails
        encodings_to_try = ['utf-8', 'latin-1', 'cp1252']
        lines = None
        detected_encoding = None
        for enc in encodings_to_try:
            try:
                with open(filepath, 'r', encoding=enc) as f:
                    lines = f.readlines()
                detected_encoding = enc
                # print(f"  Successfully read with encoding: {enc}") # Optional debug
                break # Stop trying once read successfully
            except UnicodeDecodeError:
                # print(f"  Failed reading with encoding: {enc}") # Optional debug
                continue # Try next encoding
            except Exception as e:
                print(f"  Error opening file {filepath} with encoding {enc}: {e}")
                return # Stop processing this file if a non-decoding error occurs

        if lines is None:
            print(f"Error: Could not decode file {filepath} with tried encodings: {encodings_to_try}")
            return # Skip this file

        modified_lines = []
        changes_made = False

        for line in lines:
            original_line = line

            # 1. Process Question Lines (Q1-Q9 formatting and space removal)
            match_q = question_regex.match(line)
            if match_q:
                prefix = match_q.group(1) # 'Q'
                number_str = match_q.group(2)
                separator_part = match_q.group(3) # e.g., ' -' or ' .' or '-'

                # Format number (add leading zero if 1-9)
                try:
                    number_int = int(number_str)
                    if 1 <= number_int <= 9:
                        formatted_number = f"0{number_int}"
                    else:
                        formatted_number = number_str
                except ValueError:
                    formatted_number = number_str # Fallback

                # Extract only the separator character, removing leading spaces
                separator = separator_part.strip()
                if not separator: # Handle cases like Q10 where separator might be missing
                    separator = '-' # Default to hyphen if unclear

                line_start = f"{prefix}{formatted_number}{separator}"
                rest_of_line = line[match_q.end():]
                line = line_start + rest_of_line

            # 2. Process Answer Lines (Remove spaces between letters)
            match_a = answer_regex.match(line)
            if match_a:
                answer_prefix = "Réponse : " # Standardize prefix
                answer_letters_part = match_a.group(2)
                cleaned_answers = re.sub(r"\s+", "", answer_letters_part)
                # Ensure newline consistency - use original line ending or add \n
                line_ending = '\n' if not original_line.endswith('\n') else ''
                line = f"{answer_prefix}{cleaned_answers}{line_ending}"


            if line != original_line:
                changes_made = True
            modified_lines.append(line)

        # Write changes back to the file only if modifications were made
        if changes_made:
            print(f"  Changes detected. Writing back to {filepath} using {detected_encoding} encoding.")
            try:
                 with open(filepath, 'w', encoding=detected_encoding) as f:
                    f.writelines(modified_lines)
            except Exception as e:
                print(f"  Error writing back to file {filepath}: {e}")
        else:
            print("  No changes needed.")

    except FileNotFoundError:
        print(f"Error: File not found during processing: {filepath}")
    except Exception as e:
        print(f"Error processing file {filepath}: {e}")

# --- Main Execution ---
if __name__ == "__main__":
    # Set up tkinter root window (it won't be shown)
    root = tk.Tk()
    root.withdraw() # Hide the main window

    # Ask user to select a directory
    print("Please select the folder containing your question files...")
    folder_path = filedialog.askdirectory(title="Select Folder with Question Files")

    if not folder_path:
        print("No folder selected. Exiting.")
    else:
        print(f"Selected folder: {folder_path}")
        files_to_process = []
        try:
            # List items in the selected directory
            for item_name in os.listdir(folder_path):
                # Check if the item ends with one of the allowed extensions (case-insensitive)
                if item_name.lower().endswith(ALLOWED_EXTENSIONS):
                    full_path = os.path.join(folder_path, item_name)
                    # Check if it's actually a file (and not a directory ending with .txt for example)
                    if os.path.isfile(full_path):
                        files_to_process.append(full_path)
        except FileNotFoundError:
             print(f"Error: The selected folder path does not exist: {folder_path}")
             files_to_process = [] # Ensure list is empty
        except Exception as e:
            print(f"Error reading directory {folder_path}: {e}")
            files_to_process = [] # Ensure list is empty


        if not files_to_process:
            print(f"No files with extensions {ALLOWED_EXTENSIONS} found in the selected folder.")
        else:
            print(f"Found {len(files_to_process)} file(s) with allowed extensions to process.")
            # Backup warning
            print("\n--- IMPORTANT ---")
            print("This script modifies files IN PLACE.")
            print("Please ensure you have a BACKUP of your files before proceeding.")
            confirm = input("Type 'yes' to continue processing: ").strip().lower()
            print("-----------------\n")

            if confirm == 'yes':
                for f_path in files_to_process:
                    process_file(f_path)
                print("\nProcessing complete.")
            else:
                print("Processing cancelled by user.")

    # Keep the console window open until user presses Enter (optional)
    # input("Press Enter to exit...")