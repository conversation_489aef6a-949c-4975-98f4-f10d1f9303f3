import os
import csv
import tkinter as tk
from tkinter import filedialog
import unicodedata # For Unicode normalization

# --- CONFIGURATION ---
RENAME_TASKS_FILE = "rename_tasks.txt"
DRY_RUN = False
# --- E<PERSON> CONFIGURATION ---

def clean_filename(filename_str):
    """Aggressively cleans a filename string."""
    if not isinstance(filename_str, str):
        return ""

    # Define a list of problematic quote-like characters and their replacements
    # Add to this list if you discover more!
    quote_replacements = {
        '\u2018': "'",  # LEFT SINGLE QUOTATION MARK
        '\u2019': "'",  # RIGHT SINGLE QUOTATION MARK (VERY COMMON 'SMART QUOTE')
        '\u02BC': "'",  # MODIFIER LETTER APOSTROPHE
        '\u201B': "'",  # SINGLE HIGH-REVERSED-9 QUOTATION MARK
        '\u2032': "'",  # PRIME (often used as minutes/feet, but can appear)
        # Add more if needed based on what you find
        # For the L with caron, if that's what it is:
        # 'ľ': 'l', # Or decide how you want to handle it. Usually it's intentional.
        # If it's truly the character from the image, it's most likely U+2019
    }

    # Apply specific quote replacements first
    cleaned_name = filename_str
    for bad_char, good_char in quote_replacements.items():
        cleaned_name = cleaned_name.replace(bad_char, good_char)

    # Then, normalize Unicode to handle different representations of the same character
    # NFKC is good for compatibility, collapsing things like ligatures or different space types
    cleaned_name = unicodedata.normalize('NFKC', cleaned_name)

    # Replace common problematic whitespace characters with a standard space,
    # then strip leading/trailing standard spaces.
    cleaned_name = cleaned_name.replace('\u00A0', ' ')  # Replace NBSP with regular space
    # Add any other specific characters you suspect if NBSP isn't the only one
    # e.g., cleaned_name = cleaned_name.replace('\u200B', '') # Zero-width space

    cleaned_name = cleaned_name.strip()  # Remove leading/trailing standard whitespace
    return cleaned_name

# ... (select_folder_dialog and process_rename_tasks remain the same as your last working version)
def select_folder_dialog(title="Select Folder"):
    root = tk.Tk()
    root.withdraw()
    folder_path = filedialog.askdirectory(title=title)
    root.destroy()
    return folder_path

def process_rename_tasks(filepath, path_to_modify_folder):
    actions_to_perform = []
    if not path_to_modify_folder:
        print("ERROR: No 'Folder to Modify' was selected.")
        return []

    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            for i, row in enumerate(reader):
                if not row or len(row) < 2:
                    print(f"Skipping malformed row {i+1} in '{filepath}': {row}. Expected 2 columns.")
                    continue

                current_filename_raw = row[0]
                suggested_new_filename_raw = row[1]

                current_filename_cleaned = clean_filename(current_filename_raw)
                suggested_new_filename_cleaned = clean_filename(suggested_new_filename_raw)
                
                if not current_filename_cleaned or not suggested_new_filename_cleaned:
                    print(f"Skipping row {i+1} due to empty current or new filename after cleaning. Raw: {row}")
                    continue
                    
                if suggested_new_filename_cleaned.upper() == "NO_MATCH":
                    # Pass raw name for better NO_MATCH logging if needed
                    print(f"INFO: '{current_filename_raw}' in '{os.path.basename(path_to_modify_folder)}' marked as NO_MATCH. Skipping.")
                    continue

                # We construct the 'new_path' here, but 'old_path' will be determined dynamically later
                # For now, 'old_name_cleaned' is the *target* name we're looking for
                new_full_path = os.path.join(path_to_modify_folder, suggested_new_filename_cleaned)

                actions_to_perform.append({
                    # "old_path": old_full_path, # Will be set dynamically
                    "new_path": new_full_path,
                    "folder_name": os.path.basename(path_to_modify_folder),
                    "old_name_raw_from_txt": current_filename_raw, 
                    "old_name_cleaned_target": current_filename_cleaned, # This is the name we're trying to find
                    "new_name_cleaned": suggested_new_filename_cleaned
                })

    except FileNotFoundError:
        print(f"ERROR: Rename tasks file not found at '{filepath}'.")
        return []
    except Exception as e:
        print(f"ERROR reading rename tasks file '{filepath}': {e}")
        return []
    return actions_to_perform


def execute_renames(actions):
    if not actions:
        print("No valid actions to perform.")
        return

    # Assuming all actions are for the same folder, get it from the first action
    # This requires process_rename_tasks to have set 'folder_name'
    if not actions[0].get('folder_name'):
        print("ERROR: Folder name not found in action items. Cannot proceed.")
        return
    
    # Get the full path to the directory being modified from the first action's new_path
    # This assumes all actions are within the same directory.
    parent_dir_of_modification = os.path.dirname(actions[0]["new_path"])


    print(f"\n--- Proposed Renames in Folder: '{parent_dir_of_modification}' ---")
    
    actual_actions_to_perform = [] # We'll populate this with verified old paths

    # --- Pre-Verification Step ---
    print("\n--- Verifying source files and paths ---")
    try:
        files_in_os_dir = os.listdir(parent_dir_of_modification)
        # Normalize filenames from OS for reliable comparison
        # We'll use NFC as a common target normalization form for comparison
        normalized_files_in_os_dir = {unicodedata.normalize('NFC', f): f for f in files_in_os_dir}
    except FileNotFoundError:
        print(f"ERROR: Directory not found: {parent_dir_of_modification}")
        return
    except Exception as e:
        print(f"ERROR listing directory {parent_dir_of_modification}: {e}")
        return

    for action_intent in actions:
        target_old_name_from_txt_cleaned = action_intent["old_name_cleaned_target"]
        # Normalize the target name from TXT (which was already NFKC'd and stripped) to NFC for comparison
        normalized_target_old_name = unicodedata.normalize('NFC', target_old_name_from_txt_cleaned)
        
        actual_os_filename_found = None
        
        # Search for a match in the OS directory listing
        if normalized_target_old_name in normalized_files_in_os_dir:
            actual_os_filename_found = normalized_files_in_os_dir[normalized_target_old_name]
        
        if actual_os_filename_found:
            old_full_path = os.path.join(parent_dir_of_modification, actual_os_filename_found)
            action_intent["old_path"] = old_full_path # Set the verified old_path
            action_intent["old_name_actual_os"] = actual_os_filename_found # Store for logging
            actual_actions_to_perform.append(action_intent)
            if action_intent["old_path"] == action_intent["new_path"]:
                 print(f"  Skipping (no change): '{action_intent['old_name_actual_os']}'")
            else:
                print(f"  Found: '{actual_os_filename_found}' (Target from TXT: '{target_old_name_from_txt_cleaned}')")
                print(f"    Rename TO: '{action_intent['new_name_cleaned']}'")
        else:
            print(f"  WARNING: Source file for target '{target_old_name_from_txt_cleaned}' (raw from TXT: '{action_intent['old_name_raw_from_txt']}') NOT FOUND in directory.")
            print(f"    (Normalized target for search: repr('{normalized_target_old_name}'))")


    if not actual_actions_to_perform:
        print("\nNo files found to rename after verification.")
        return
        
    if DRY_RUN:
        print("\nDRY RUN active. No files will be changed.")
        print("To perform actual renaming, edit the script and set DRY_RUN = False.")
        return

    print(f"\n--- Performing Renames in Folder: '{parent_dir_of_modification}' ---")
    confirm = input(f"Proceed with actual renaming files in '{parent_dir_of_modification}'? (yes/no): ").strip().lower()
    if confirm != 'yes':
        print("Renaming cancelled by user.")
        return

    renamed_count = 0
    error_count = 0
    for action in actual_actions_to_perform: # Iterate over verified actions
        if action["old_path"] == action["new_path"]: # Already logged as skipping
            continue

        try:
            # Double check existence with the exact path found
            if not os.path.exists(action["old_path"]):
                print(f"ERROR (unexpected): Source file disappeared before rename: '{action['old_path']}'. Skipping.")
                error_count += 1
                continue

            if os.path.exists(action["new_path"]):
                print(f"WARNING: Target file already exists: {action['new_path']}. Skipping rename for '{action['old_name_actual_os']}'.")
                error_count += 1
                continue

            os.rename(action["old_path"], action["new_path"])
            print(f"SUCCESS: Renamed '{action['old_name_actual_os']}' to '{action['new_name_cleaned']}'")
            renamed_count += 1
        except Exception as e:
            print(f"ERROR renaming '{action['old_name_actual_os']}' to '{action['new_name_cleaned']}': {e}")
            error_count += 1
    
    print(f"\n--- Summary for Folder: '{parent_dir_of_modification}' ---")
    print(f"Files successfully renamed: {renamed_count}")
    print(f"Errors or files skipped: {error_count}")


if __name__ == "__main__":
    # ... (rest of the __main__ block is the same as rename_files_simplified.py) ...
    print("--- File Renaming Utility ---")
    print("You will be asked to select the FOLDER TO MODIFY.")
    print("This is the folder where files will be renamed based on 'rename_tasks.txt'.")
    print("Ensure 'rename_tasks.txt' is in the same directory as this script and contains:")
    print("  CURRENT_FILENAME_IN_MODIFY_FOLDER,SUGGESTED_NEW_FILENAME_FROM_REFERENCE")
    print("-" * 30)

    folder_to_modify_path = select_folder_dialog(title="Select the FOLDER TO MODIFY")
    if not folder_to_modify_path:
        print("No folder selected to modify. Exiting.")
        exit()
    print(f"Folder to modify selected: {folder_to_modify_path}")
    print("-" * 30)

    script_dir = os.path.dirname(os.path.abspath(__file__))
    tasks_file_path = os.path.join(script_dir, RENAME_TASKS_FILE)
    
    print(f"Looking for rename tasks in: '{tasks_file_path}'")
    print("IMPORTANT: Consider backing up your 'Folder to Modify' before proceeding with actual renaming (when DRY_RUN is False).")
    print("-" * 30)
        
    rename_plan = process_rename_tasks(tasks_file_path, folder_to_modify_path)
    
    if rename_plan:
        execute_renames(rename_plan)
    else:
        print("No rename tasks were processed. Check your selection or the content of rename_tasks.txt.")