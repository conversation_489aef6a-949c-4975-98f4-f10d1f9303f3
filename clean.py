import os
from docx import Document
import time # Optional: to add a small delay

def process_doc_like_paste_text(doc_path):
    """
    Opens a DOCX file, extracts only the text content paragraph by paragraph,
    skips empty/whitespace-only paragraphs, and saves this plain text
    content back to the original file path, overwriting it.
    This mimics the effect of 'Paste as Text' by discarding formatting.

    Args:
        doc_path (str): The full path to the DOCX file.

    Returns:
        bool: True if successful, False otherwise.
    """
    try:
        print(f"Processing (Paste Text Style): {os.path.basename(doc_path)}...")
        original_document = Document(doc_path)
        new_document = Document() # Create a new blank document

        has_content = False
        # Iterate through paragraphs in the original document
        for para in original_document.paragraphs:
            text = para.text
            # Check if the extracted text is non-empty after stripping whitespace
            if text.strip():
                # Add the non-empty text as a new paragraph to the new document
                # This step inherently loses all original formatting
                new_document.add_paragraph(text)
                has_content = True
            # else: paragraph text is empty or whitespace-only, so skip it

        # --- Important Note ---
        # This method ONLY preserves the text content. All formatting,
        # tables, images, headers, footers, etc., from the original
        # document will be LOST.

        # Overwrite the original file with the cleaned, text-only content
        new_document.save(doc_path)
        print(f"-> Saved cleaned (text only) content to: {os.path.basename(doc_path)}")
        return True

    except Exception as e:
        print(f"!! Error processing {os.path.basename(doc_path)}: {e}")
        # You might want to add more specific error handling if needed
        # For example, skip files that python-docx cannot open at all.
        # Consider printing the exception traceback for debugging:
        # import traceback
        # print(traceback.format_exc())
        return False

def process_directory(dir_path):
    """
    Finds all .docx files in the specified directory and processes them
    using the 'paste text style' method.
    """
    if not os.path.isdir(dir_path):
        print(f"Error: Directory not found: {dir_path}")
        return

    print("-" * 40)
    print(f"Scanning directory: {dir_path}")
    print("⚠️ WARNING: Files will be OVERWRITTEN in place.")
    print("⚠️ WARNING: ALL original formatting (bold, italics, tables, images, etc.)")
    print("           in the files WILL BE LOST by this script.")
    print("Please ensure you have backups before proceeding.")
    # Optional pause to let the user read the warning
    # time.sleep(5)
    print("-" * 40)

    processed_files = 0
    failed_files = 0

    for filename in os.listdir(dir_path):
        # Make sure it's a docx file and not a temporary Word file (starting with ~$)
        if filename.lower().endswith(".docx") and not filename.startswith("~$"):
            full_path = os.path.join(dir_path, filename)
            if process_doc_like_paste_text(full_path):
                processed_files += 1
            else:
                failed_files += 1
        elif filename.startswith("~$"):
             print(f"-> Skipping temporary file: {filename}")


    print("-" * 40)
    print("Processing complete.")
    print(f"Successfully processed (text-only): {processed_files} files.")
    if failed_files > 0:
        print(f"Failed to process: {failed_files} files (check errors above).")
    print("-" * 40)

# --- Main Execution ---
if __name__ == "__main__":
    # It's often better to specify the directory directly in the script
    # or use command-line arguments, but input works too.
    target_directory = input("Enter the full path to the directory containing your DOCX files: ")
    # Basic validation/cleaning of the path
    target_directory = target_directory.strip().strip('"').strip("'")
    process_directory(target_directory)