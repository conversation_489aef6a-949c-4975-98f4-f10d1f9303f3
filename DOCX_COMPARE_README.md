# DOCX Filename Comparison Tool

A tool for comparing DOCX filenames between different folders, identifying mismatches, and generating rename tasks.

## Background

This tool was created to solve the problem of ensuring filename consistency when uploading question files (QCMs) and their corresponding comment files to a QCM platform. The system uses filenames to create categories/subcategories in the database, so filenames must match exactly between uploads.

## Features

- **Folder Comparison**: Compare DOCX filenames between two or more folders
- **Missing File Detection**: Identify files that exist in one folder but not in others
- **Exact Match Detection**: Find files with identical names across folders (including files that differ only by the case of the first letter)
- **Similarity Detection**: Find files with similar but not identical names using fuzzy matching
- **Reference Folder**: Designate a specific folder as the reference against which all others are compared
- **Direct File Renaming**: Rename files directly from the interface with backup creation
- **Adjustable Similarity Threshold**: Control how strict or lenient the similarity matching should be
- **Results Export**: Save comparison results to a text file for reference

## Usage

1. **Launch the Application**:
   ```
   python docx_compare.py
   ```

2. **Add Folders**:
   - Click "Add Folder" to select folders containing DOCX files
   - Add at least two folders for comparison

3. **Set Reference Folder** (Optional):
   - Select a folder in the list
   - Click "Set Selected as Reference"
   - If no reference is set, the first folder is used as reference

4. **Adjust Similarity Threshold** (Optional):
   - Move the slider to set how similar filenames need to be to be considered matches
   - Higher values (closer to 1.0) require more similarity
   - Lower values (closer to 0.1) are more lenient

5. **Compare Folders**:
   - Click "Compare Folders" to start the comparison
   - Results will appear in the "Missing Files" and "Matching Files" tabs

6. **Review Initial Results**:
   - The "Missing Files" tab shows files that exist in the reference folder but not in other folders
   - The "Matching Files" tab shows exact matches between folders

7. **Find Similar Files** (Optional):
   - Click "Find Similar Files" to search for files with similar names
   - This will analyze missing files and find potential matches based on the similarity threshold
   - Results will appear in the "Matching Files" tab under the exact matches

8. **Select Files for Renaming** (After finding similar files):
   - In the "Matching Files" tab, check the boxes next to similar matches you want to rename
   - These selections will be used when generating rename tasks

9. **Export Results** (Optional):
   - Click "Export Results" to save the comparison results to a text file
   - The export will include missing files, exact matches, and similar files (if found)

10. **Rename Selected Files**:
    - After selecting files for renaming, click "Rename Selected Files"
    - Confirm the rename operation when prompted
    - The program will rename the files directly and create backups if needed
    - After renaming, the folders will be automatically refreshed to show the new filenames

## Workflow Example

1. Add a folder containing question files as the reference folder
2. Add a folder containing comment files
3. Compare the folders to find mismatches and exact matches
4. Click "Find Similar Files" to identify similar filenames
5. Select similar files that should be renamed to match the reference
6. Click "Rename Selected Files" to perform the renaming directly
7. Review the results after the folders are automatically refreshed

## Requirements

- Python 3.6 or higher
- tkinter (usually included with Python)
- No external dependencies beyond the standard library

## Tips for Effective Use

- **Reference Folder**: Choose the folder that contains the "correct" filenames as your reference
- **Similarity Threshold**: Start with a higher threshold (0.75-0.85) and lower it if needed
- **Multiple Folders**: You can compare more than two folders at once
- **Rename Tasks**: The generated rename_tasks.txt file follows the format expected by rename.py
- **Large Folders**: For folders with many files, the comparison may take a moment to complete

## Handling Special Cases

- **Files in Only One Folder**: These will be listed in the "Missing Files" tab
- **Case-Sensitive Matching**: Files that differ only by the case of the first letter (e.g., "Document.docx" and "document.docx") are considered exact matches
- **Multiple Similar Matches**: All potential matches above the similarity threshold will be shown
- **Non-DOCX Files**: The tool only considers files with the .docx extension

## Case Sensitivity Handling

The program has special handling for case sensitivity in filenames:

1. **First-Letter Case Differences**: Files that are identical except for the case of the first letter are treated as exact matches
   - Example: "Report.docx" and "report.docx" would be considered exact matches
   - These will be displayed in the format: "Report.docx (matches report.docx)"

2. **Other Case Differences**: Files with case differences in other positions are not automatically considered exact matches
   - Example: "ReportA.docx" and "reporta.docx" would be evaluated based on similarity

## File Renaming and Backup

When renaming files, the program includes safety features:

1. **Confirmation Dialog**: Before any renaming occurs, you must confirm the operation
2. **File Existence Checks**: The program verifies that source files exist before attempting to rename them
3. **Overwrite Protection**: If a target filename already exists, you'll be asked to confirm overwriting
4. **Automatic Backups**: When overwriting an existing file, the original is backed up with a .bak extension
5. **Error Handling**: Detailed error messages are provided if any rename operations fail
6. **Automatic Refresh**: After renaming, folders are automatically rescanned to show the updated filenames
